# 架构概览

> **面向CTO和架构师的技术架构分析**

## 📋 文档说明

### 分析方法论
本文档基于以下理论框架进行架构分析：
- **系统思维理论**: 从整体性角度分析架构特点和依赖关系
- **TOGAF架构框架**: 采用分层架构视图和标准化分析方法
- **风险管理理论**: 识别架构风险和技术债务

### 目标受众
- **CTO**: 关注架构决策和技术战略
- **架构师**: 关注设计原则和技术实现
- **技术负责人**: 关注系统优化和演进方向

详细的方法论说明请参考：[方法论框架](../methodology/FRAMEWORK.md)

## 🏗️ 现有架构分析

### 当前架构特征
- **微服务架构**: 已按业务域和供应商维度拆分为24个服务
- **分层设计**: 实际部署为前端、网关、业务、数据四层架构
- **技术多样性**: 现有Java、Python、C++多技术栈并存
- **容器化部署**: 已在Kubernetes集群中运行

### 架构现状评估
- **部署状态**: 所有服务已上线运行
- **依赖关系**: 89个依赖关系已建立并稳定运行
- **业务支撑**: 当前支持8个供应商的竞价业务
- **技术债务**: 存在一些架构优化空间（详见技术债务分析）

### 系统拓扑

> **注**: 基于代码分析修正了依赖关系，详见 [拓扑依赖分析报告](TOPOLOGY_DEPENDENCY_ANALYSIS.md)
> **标准**: 遵循ArchiMate 3.1规范的图形符号和颜色编码

#### 📋 拓扑图导航

为了更清晰地展示系统架构，我们将完整系统拓扑按逻辑分组划分为以下子图：

| 子图编号 | 覆盖范围 | 服务数量 | 依赖关系数 |
|---------|---------|---------|-----------|
| [🏗️ 完整系统拓扑](#完整系统拓扑) | 全部服务和依赖 | 24个服务 | 98个依赖 |
| [📱 子图1: 前端认证层](#子图1-前端认证层) | 前端应用 + 认证网关 | 8个服务 | 15个依赖 |
| [🏢 子图2: 核心管理层](#子图2-核心管理层) | 管理服务 + 通用服务 | 4个服务 | 12个依赖 |
| [📡 子图3: 传统供应商业务](#子图3-传统供应商业务) | AT&T + B-Stock + Sprint业务 | 4个服务 | 12个依赖 |
| [🎯 子图4: Galaxy业务生态](#子图4-galaxy业务生态) | Galaxy系列 + 在线竞价 | 4个服务 | 8个依赖 |
| [📋 子图5: 订单管理关系](#子图5-订单管理关系) | bid_orders与其他服务关系 | 4个服务 | 7个依赖 |
| [📊 子图6: 管理服务关系](#子图6-管理服务关系) | bid-system-admin与其他服务关系 | 4个服务 | 9个依赖 |
| [📈 子图7: 数据服务关系](#子图7-数据服务关系) | docomo与其他服务关系 | 3个服务 | 3个依赖 |
| [⚡ 子图8: 数据处理层](#子图8-数据处理层) | 数据处理 + 监控服务 | 9个服务 | 25个依赖 |
| [🔧 子图9: 基础设施层](#子图9-基础设施层) | 所有基础设施依赖 | 5个基础设施 | 23个依赖 |

> **完整性保证**: 所有子图的服务和依赖关系总和等于完整系统拓扑，无遗漏、无重复

#### **ArchiMate图例说明**

| 层次 | ArchiMate层 | 颜色 | 符号说明 |
|------|-------------|------|---------|
| **前端层** | 业务层 | 🟢 绿色 | 业务角色/接口 - 用户交互界面 |
| **认证门户** | 应用层 | 🔵 蓝色 | 应用服务 - SSO单点登录中心 |
| **网关层** | 应用层 | 🔵 蓝色 | 应用服务 - API网关和认证 |
| **业务层** | 应用层 | 🔵 蓝色 | 应用组件 - 业务逻辑处理 |
| **数据层** | 应用层 | 🔵 蓝色 | 应用组件 - 数据处理服务 |
| **管理层** | 应用层 | 🔵 蓝色 | 应用组件 - 监控管理服务 |
| **基础设施** | 技术层 | 🟠 橙色 | 技术服务/节点 - 基础设施组件 |

**ArchiMate 3.1 关系类型**:
- **🔵 蓝色实线箭头** `→`: 服务关系 (Serving) - 服务提供功能给其他元素
- **🟢 绿色虚线箭头** `⇢`: 流关系 (Flow) - 数据、信息或价值的传递
- **🟠 橙色点线箭头** `⇣`: 访问关系 (Access) - 对被动结构元素的访问

#### 🏗️ 完整系统拓扑

```mermaid
graph TD
    subgraph "🟢 业务层 (Business Layer)"
        FE1["👤 maxkey-web-app<br/><i>Business Interface</i>"]
        FE2["🔧 maxkey-web-mgt-app<br/><i>Business Interface</i>"]
        FE4["💼 manage_front<br/><i>Business Interface</i>"]
        FE5["🏛️ auction-management-web<br/><i>Business Interface</i>"]
        FE6["📱 galaxy-auction<br/><i>Business Interface</i>"]
    end

    subgraph "🔵 应用层 - 认证门户 (Application Layer - Auth Portal)"
        FE3["🔐 sso_project<br/><i>Application Service</i><br/>SSO认证中心"]
    end

    subgraph "🔵 应用层 - 网关服务 (Application Layer - Gateway)"
        GW["🚪 sonic-gateway<br/><i>Application Service</i>"]
        AUTH["🔐 maxkey<br/><i>Application Service</i>"]
    end

    subgraph "🔵 应用层 - 业务服务 (Application Layer - Business)"
        ADMIN["📊 bid-system-admin<br/><i>Application Component</i>"]
        COMMON["🔧 common<br/><i>Application Component</i>"]
        OFFER["💰 offerbids<br/><i>Application Component</i>"]
        ATT["📡 att-bid-system<br/><i>Application Component</i>"]
        BSTOCK["📦 bstock_bid<br/><i>Application Component</i>"]
        SPRINT["🏃 sprint_bid<br/><i>Application Component</i>"]
        SELECT["🎯 galaxy-bid-select<br/><i>Application Component</i>"]
        ASURION["🛡️ galaxy-bid-asurion<br/><i>Application Component</i>"]
        RENUGO["♻️ galaxy-bid-renugo<br/><i>Application Component</i>"]
        ONLINE["🌐 online-bidding<br/><i>Application Component</i>"]
    end

    subgraph "🔵 应用层 - 数据服务 (Application Layer - Data)"
        EXPRESS["⚡ express<br/><i>Application Component</i>"]
        SYNC["🔄 syncdata<br/><i>Application Component</i>"]
        HYLA["📈 hyla<br/><i>Application Component</i>"]
        PUSH1["📤 push-access-service<br/><i>Application Component</i>"]
        PUSH2["📨 push-send-service<br/><i>Application Component</i>"]
        ORDERS["📋 bid_orders<br/><i>Application Component</i>"]
        DOCOMO["📊 docomo<br/><i>Application Component</i>"]
        ERP["🏢 galaxy_odoo_erp<br/><i>Application Component</i>"]
    end

    subgraph "🔵 应用层 - 管理服务 (Application Layer - Management)"
        MONITOR["📊 sonic-admin<br/><i>Application Component</i>"]
        SCHEDULER["⏰ xxl-job<br/><i>Application Component</i>"]
    end

    subgraph "🟠 技术层 (Technology Layer)"
        REDIS["💾 Redis集群<br/><i>Technology Service</i>"]
        RABBITMQ["📮 RabbitMQ<br/><i>Technology Service</i>"]
        MYSQL["🗄️ MySQL<br/><i>Technology Service</i>"]
        POSTGRESQL["🐘 PostgreSQL<br/><i>Technology Service</i>"]
        MONGODB["📄 MongoDB<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving) - 服务提供功能给其他元素
    FE1 --> FE3
    FE2 --> FE3
    FE4 --> FE3
    FE5 --> FE3

    FE3 --> GW
    FE3 --> AUTH

    GW --> AUTH
    GW --> ADMIN

    ADMIN --> COMMON
    ADMIN --> ATT
    ADMIN --> BSTOCK
    ADMIN --> SPRINT
    ADMIN --> SELECT
    ADMIN --> ASURION
    ADMIN --> RENUGO
    ADMIN --> ONLINE

    ATT --> COMMON
    BSTOCK --> COMMON
    SPRINT --> COMMON

    OFFER --> EXPRESS
    EXPRESS --> COMMON

    %% 🟢 流关系 (Flow) - 数据、信息或价值的传递
    COMMON -.-> PUSH1
    PUSH1 -.-> PUSH2

    OFFER -.-> RABBITMQ
    COMMON -.-> RABBITMQ
    PUSH1 -.-> RABBITMQ

    %% 🟠 访问关系 (Access) - 对被动结构元素的访问
    ADMIN -.-> REDIS
    COMMON -.-> REDIS
    ATT -.-> REDIS
    BSTOCK -.-> REDIS
    SPRINT -.-> REDIS

    ADMIN -.-> MONGODB
    ONLINE -.-> MYSQL
    AUTH -.-> MYSQL

    GW -.-> NACOS
    AUTH -.-> NACOS
    ADMIN -.-> NACOS

    %% 🔵 补充的服务关系 (之前遗漏)
    FE6 --> FE3
    FE6 --> GW
    FE6 --> ATT
    FE6 --> BSTOCK
    FE6 --> SPRINT
    FE6 --> SELECT
    FE6 --> ASURION
    FE6 --> ONLINE

    SELECT --> COMMON
    SELECT --> GW
    ASURION --> COMMON
    ASURION --> GW
    RENUGO --> COMMON
    RENUGO --> GW
    ONLINE --> ADMIN
    SYNC --> ADMIN
    OFFER --> HYLA

    MONITOR --> GW
    MONITOR --> AUTH
    MONITOR --> ADMIN

    %% 🔵 bid_orders服务的依赖关系
    ORDERS --> COMMON
    ORDERS --> HYLA
    ORDERS --> ATT
    ORDERS --> BSTOCK
    ORDERS --> SPRINT
    ORDERS --> SELECT
    ORDERS --> ASURION

    %% 🔵 docomo服务的依赖关系
    DOCOMO --> COMMON

    %% 🟢 补充的流关系
    SYNC -.-> RABBITMQ
    HYLA -.-> RABBITMQ

    %% 🟠 补充的访问关系
    SELECT -.-> REDIS
    ASURION -.-> REDIS
    RENUGO -.-> REDIS
    ONLINE -.-> REDIS
    HYLA -.-> REDIS
    ORDERS -.-> REDIS
    ORDERS -.-> MYSQL
    DOCOMO -.-> REDIS
    MONITOR -.-> NACOS
    SCHEDULER -.-> NACOS
    SCHEDULER -.-> MYSQL

    %% 🟠 补充遗漏的基础设施访问关系
    EXPRESS -.-> REDIS
    SYNC -.-> REDIS
    PUSH1 -.-> REDIS
    PUSH2 -.-> REDIS
    COMMON -.-> NACOS
    OFFER -.-> NACOS
    ATT -.-> NACOS
    BSTOCK -.-> NACOS
    SPRINT -.-> NACOS
    SELECT -.-> NACOS
    ASURION -.-> NACOS
    RENUGO -.-> NACOS
    ONLINE -.-> NACOS
    EXPRESS -.-> NACOS
    SYNC -.-> NACOS
    HYLA -.-> NACOS
    PUSH1 -.-> NACOS
    PUSH2 -.-> NACOS
    ORDERS -.-> NACOS
    DOCOMO -.-> NACOS

    %% 🔵 ERP 服务关系
    ERP --> AUTH
    ADMIN --> ERP
    ONLINE --> ERP
    FE6 --> ERP

    %% 🟢 ERP 流关系
    ERP -.-> RABBITMQ

    %% 🟠 ERP 访问关系
    ERP -.-> REDIS
    ERP -.-> POSTGRESQL

    %% ArchiMate 3.1 标准颜色规范
    classDef businessLayer fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    %% 业务层 (绿色) - Business Layer
    class FE1,FE2,FE4,FE5,FE6 businessLayer

    %% 应用层 (蓝色) - Application Layer
    class FE3,GW,AUTH,ADMIN,COMMON,OFFER,ATT,BSTOCK,SPRINT,SELECT,ASURION,RENUGO,ONLINE applicationLayer
    class EXPRESS,SYNC,HYLA,PUSH1,PUSH2,ORDERS,DOCOMO,MONITOR,SCHEDULER applicationLayer

    %% 技术层 (橙色) - Technology Layer
    class REDIS,RABBITMQ,MYSQL,MONGODB,NACOS technologyLayer

    %% 🔵 服务关系 (Serving) - 蓝色实线箭头
    linkStyle 0 stroke:#1976d2,stroke-width:2px
    linkStyle 1 stroke:#1976d2,stroke-width:2px
    linkStyle 2 stroke:#1976d2,stroke-width:2px
    linkStyle 3 stroke:#1976d2,stroke-width:2px
    linkStyle 4 stroke:#1976d2,stroke-width:2px
    linkStyle 5 stroke:#1976d2,stroke-width:2px
    linkStyle 6 stroke:#1976d2,stroke-width:2px
    linkStyle 7 stroke:#1976d2,stroke-width:2px
    linkStyle 8 stroke:#1976d2,stroke-width:2px
    linkStyle 9 stroke:#1976d2,stroke-width:2px
    linkStyle 10 stroke:#1976d2,stroke-width:2px
    linkStyle 11 stroke:#1976d2,stroke-width:2px
    linkStyle 12 stroke:#1976d2,stroke-width:2px
    linkStyle 13 stroke:#1976d2,stroke-width:2px
    linkStyle 14 stroke:#1976d2,stroke-width:2px
    linkStyle 15 stroke:#1976d2,stroke-width:2px
    linkStyle 16 stroke:#1976d2,stroke-width:2px
    linkStyle 17 stroke:#1976d2,stroke-width:2px
    linkStyle 18 stroke:#1976d2,stroke-width:2px
    linkStyle 19 stroke:#1976d2,stroke-width:2px
    linkStyle 20 stroke:#1976d2,stroke-width:2px

    %% 🟢 流关系 (Flow) - 绿色虚线箭头
    linkStyle 21 stroke:#2e7d32,stroke-width:2px,stroke-dasharray: 5 5
    linkStyle 22 stroke:#2e7d32,stroke-width:2px,stroke-dasharray: 5 5
    linkStyle 23 stroke:#2e7d32,stroke-width:2px,stroke-dasharray: 5 5
    linkStyle 24 stroke:#2e7d32,stroke-width:2px,stroke-dasharray: 5 5
    linkStyle 25 stroke:#2e7d32,stroke-width:2px,stroke-dasharray: 5 5

    %% 🟠 访问关系 (Access) - 橙色点线箭头
    linkStyle 26 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 27 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 28 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 29 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 30 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 31 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 32 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 33 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 34 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 35 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 36 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3

    %% 🔵 补充的服务关系 - 蓝色实线箭头 (继续)
    linkStyle 37 stroke:#1976d2,stroke-width:2px
    linkStyle 38 stroke:#1976d2,stroke-width:2px
    linkStyle 39 stroke:#1976d2,stroke-width:2px
    linkStyle 40 stroke:#1976d2,stroke-width:2px
    linkStyle 41 stroke:#1976d2,stroke-width:2px
    linkStyle 42 stroke:#1976d2,stroke-width:2px
    linkStyle 43 stroke:#1976d2,stroke-width:2px
    linkStyle 44 stroke:#1976d2,stroke-width:2px
    linkStyle 45 stroke:#1976d2,stroke-width:2px
    linkStyle 46 stroke:#1976d2,stroke-width:2px
    linkStyle 47 stroke:#1976d2,stroke-width:2px
    linkStyle 48 stroke:#1976d2,stroke-width:2px
    linkStyle 49 stroke:#1976d2,stroke-width:2px
    linkStyle 50 stroke:#1976d2,stroke-width:2px
    linkStyle 51 stroke:#1976d2,stroke-width:2px
    linkStyle 52 stroke:#1976d2,stroke-width:2px
    linkStyle 53 stroke:#1976d2,stroke-width:2px
    linkStyle 54 stroke:#1976d2,stroke-width:2px
    linkStyle 55 stroke:#1976d2,stroke-width:2px
    linkStyle 56 stroke:#1976d2,stroke-width:2px
    linkStyle 57 stroke:#1976d2,stroke-width:2px
    linkStyle 58 stroke:#1976d2,stroke-width:2px
    linkStyle 59 stroke:#1976d2,stroke-width:2px
    linkStyle 60 stroke:#1976d2,stroke-width:2px
    linkStyle 61 stroke:#1976d2,stroke-width:2px
    linkStyle 62 stroke:#1976d2,stroke-width:2px
    linkStyle 63 stroke:#1976d2,stroke-width:2px
    linkStyle 64 stroke:#1976d2,stroke-width:2px

    %% 🟢 补充的流关系 - 绿色虚线箭头 (继续)
    linkStyle 65 stroke:#2e7d32,stroke-width:2px,stroke-dasharray: 5 5
    linkStyle 66 stroke:#2e7d32,stroke-width:2px,stroke-dasharray: 5 5

    %% 🟠 补充的访问关系 - 橙色点线箭头 (继续)
    linkStyle 67 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 68 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 69 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 70 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 71 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 72 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 73 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 74 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 75 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 76 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 77 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 78 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 79 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 80 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 81 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 82 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 83 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 84 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 85 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 86 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 87 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 88 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 89 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 90 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 91 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 92 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 93 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 94 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 95 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 96 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
    linkStyle 97 stroke:#f57c00,stroke-width:2px,stroke-dasharray: 2 3
```

#### **详细图例说明**

##### **ArchiMate层次映射**

| 系统层次 | ArchiMate层 | 颜色编码 | 元素类型 | 说明 |
|---------|-------------|---------|---------|------|
| **前端层** | 🟢 业务层 | 绿色 `#c8e6c9` | Business Interface | 用户交互界面，代表业务角色的接触点 |
| **认证门户** | 🔵 应用层 | 蓝色 `#bbdefb` | Application Service | SSO单点登录中心，为所有前端应用提供统一认证 |
| **网关层** | 🔵 应用层 | 蓝色 `#bbdefb` | Application Service | 应用服务，提供API网关和认证功能 |
| **业务层** | 🔵 应用层 | 蓝色 `#bbdefb` | Application Component | 应用组件，实现核心业务逻辑 |
| **数据层** | 🔵 应用层 | 蓝色 `#bbdefb` | Application Component | 应用组件，负责数据处理和传输 |
| **管理层** | 🔵 应用层 | 蓝色 `#bbdefb` | Application Component | 应用组件，提供监控和管理功能 |
| **基础设施** | 🟠 技术层 | 橙色 `#ffcc80` | Technology Service | 技术服务，提供基础设施支撑 |

##### **图形符号说明**

| 符号 | ArchiMate元素 | 用途 | 示例 |
|------|---------------|------|------|
| 👤 | Business Interface | 用户界面 | maxkey-web-app |
| 🚪 | Application Service | 应用服务 | sonic-gateway |
| 📊 | Application Component | 应用组件 | bid-system-admin |
| 💾 | Technology Service | 技术服务 | Redis集群 |

##### **ArchiMate 3.1 关系类型说明**

| 关系样式 | ArchiMate关系 | 符号 | 含义 | 使用场景 | 示例 |
|---------|---------------|------|------|---------|------|
| 🔵 **蓝色实线箭头** | Serving | `→` | 服务关系 | 元素为其他元素提供功能或服务 | 前端→认证服务、网关→业务服务 |
| 🟢 **绿色虚线箭头** | Flow | `⇢` | 流关系 | 数据、信息或价值在元素间的传递 | 服务⇢消息队列、数据推送流程 |
| 🟠 **橙色点线箭头** | Access | `⇣` | 访问关系 | 行为元素对被动结构元素的访问 | 服务⇣数据库、服务⇣缓存、服务⇣配置中心 |

##### **颜色编码规范**

```yaml
ArchiMate 3.1 标准颜色:
  # 组件层次颜色
  业务层 (Business Layer):
    - 填充色: #c8e6c9 (浅绿色)
    - 边框色: #2e7d32 (深绿色)
    - 文字色: #1b5e20 (墨绿色)

  应用层 (Application Layer):
    - 填充色: #bbdefb (浅蓝色)
    - 边框色: #1976d2 (深蓝色)
    - 文字色: #0d47a1 (墨蓝色)

  技术层 (Technology Layer):
    - 填充色: #ffcc80 (浅橙色)
    - 边框色: #f57c00 (深橙色)
    - 文字色: #e65100 (墨橙色)

  # ArchiMate 3.1 关系类型样式
  关系类型 (Relationship Types):
    服务关系 (Serving):
      - 颜色: #1976d2 (蓝色)
      - 样式: 实线箭头 (solid)
      - 用途: 服务提供功能给其他元素

    流关系 (Flow):
      - 颜色: #2e7d32 (绿色)
      - 样式: 虚线箭头 (dashed: 5 5)
      - 用途: 数据、信息或价值的传递

    访问关系 (Access):
      - 颜色: #f57c00 (橙色)
      - 样式: 点线箭头 (dotted: 2 3)
      - 用途: 对被动结构元素的访问

  # 特殊标识
  新增元素标识:
    - 边框色: #d32f2f (红色)
    - 边框宽度: 3px (加粗)
    - 用于标识本次更新新增的服务
```

##### **特殊组件说明**

**sso_project (SSO认证中心)** 的特殊地位：

| 特征 | 说明 | 影响 |
|------|------|------|
| **功能定位** | 单点登录认证中心，不是普通业务应用 | 所有前端应用都依赖它进行身份认证 |
| **依赖关系** | 其他前端应用 → sso_project → 认证服务 | 形成星型认证架构 |
| **技术实现** | Vue 3 + OAuth2，支持跨域认证和token传递 | 实现真正的单点登录体验 |
| **架构重要性** | 认证入口点，单点故障风险高 | 需要高可用性设计和监控 |
| **层次归属** | 虽然是前端技术，但功能上属于应用服务层 | 在业务层和应用层之间起桥梁作用 |

**认证流程**: 用户访问任何前端应用 → 重定向到sso_project → 完成认证 → 返回原应用

##### **架构视图类型**

根据ArchiMate规范，当前视图属于：
- **视图类型**: 应用架构视图 (Application Architecture View)
- **关注点**: 应用结构 (Application Structure)
- **利益相关者**: 应用架构师、开发团队、运维团队
- **目的**: 展示应用组件及其相互关系

### 📋 系统拓扑子图

#### 📱 子图1: 前端认证层

> **覆盖范围**: 所有前端应用、认证服务、网关服务及其相互依赖关系
> **服务数量**: 8个服务 | **依赖关系**: 15个

```mermaid
graph TD
    subgraph "🟢 业务层 - 前端应用"
        FE1["👤 maxkey-web-app<br/><i>Business Interface</i>"]
        FE2["🔧 maxkey-web-mgt-app<br/><i>Business Interface</i>"]
        FE4["💼 manage_front<br/><i>Business Interface</i>"]
        FE5["🏛️ auction-management-web<br/><i>Business Interface</i>"]
        FE6["📱 galaxy-auction<br/><i>Business Interface</i>"]
    end

    subgraph "🔵 应用层 - 认证门户"
        FE3["🔐 sso_project<br/><i>Application Service</i><br/>SSO认证中心"]
    end

    subgraph "🔵 应用层 - 网关服务"
        GW["🚪 sonic-gateway<br/><i>Application Service</i>"]
        AUTH["🔐 maxkey<br/><i>Application Service</i>"]
    end

    subgraph "🟠 技术层 - 基础设施"
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
        MYSQL["🗄️ MySQL<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    FE1 --> FE3
    FE2 --> FE3
    FE4 --> FE3
    FE5 --> FE3
    FE6 --> FE3

    FE3 --> GW
    FE3 --> AUTH
    GW --> AUTH

    %% 🟠 访问关系 (Access)
    GW -.-> NACOS
    AUTH -.-> NACOS
    AUTH -.-> MYSQL

    %% ArchiMate 3.1 样式
    classDef businessLayer fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class FE1,FE2,FE4,FE5,FE6 businessLayer
    class FE3,GW,AUTH applicationLayer
    class NACOS,MYSQL technologyLayer
```

#### 🏢 子图2: 核心管理层

> **覆盖范围**: 核心管理服务、通用服务及其相互依赖关系
> **服务数量**: 4个服务 | **依赖关系**: 12个

```mermaid
graph TD
    subgraph "🔵 应用层 - 网关服务"
        GW["🚪 sonic-gateway<br/><i>Application Service</i><br/>API网关"]
        AUTH["🔐 maxkey<br/><i>Application Service</i><br/>认证服务"]
    end

    subgraph "🔵 应用层 - 核心管理"
        ADMIN["📊 bid-system-admin<br/><i>Application Component</i><br/>竞价管理中心"]
        COMMON["🔧 common<br/><i>Application Component</i><br/>通用业务服务"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i>"]
        MYSQL["🗄️ MySQL<br/><i>Technology Service</i>"]
        MONGODB["📄 MongoDB<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    GW --> AUTH
    GW --> ADMIN
    ADMIN --> COMMON

    %% 🟠 访问关系 (Access)
    GW -.-> NACOS
    AUTH -.-> NACOS
    AUTH -.-> MYSQL
    ADMIN -.-> REDIS
    ADMIN -.-> MONGODB
    ADMIN -.-> NACOS
    COMMON -.-> REDIS
    COMMON -.-> NACOS

    %% ArchiMate 3.1 样式
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class GW,AUTH,ADMIN,COMMON applicationLayer
    class REDIS,MYSQL,MONGODB,NACOS technologyLayer
```

#### 📡 子图3: 传统供应商业务

> **覆盖范围**: AT&T、B-Stock、Sprint传统供应商业务服务及其依赖关系
> **服务数量**: 6个服务 | **依赖关系**: 15个

```mermaid
graph TD
    subgraph "🔵 应用层 - 核心服务接口"
        ADMIN["📊 bid-system-admin<br/><i>Application Component</i>"]
        COMMON["🔧 common<br/><i>Application Component</i>"]
        ORDERS["📋 bid_orders<br/><i>Application Component</i>"]
    end

    subgraph "🔵 应用层 - 传统供应商业务"
        ATT["📡 att-bid-system<br/><i>Application Component</i><br/>AT&T竞价服务"]
        BSTOCK["📦 bstock_bid<br/><i>Application Component</i><br/>B-Stock竞价服务"]
        SPRINT["🏃 sprint_bid<br/><i>Application Component</i><br/>Sprint竞价服务"]
    end

    subgraph "🔵 应用层 - 移动端应用"
        FE6["📱 galaxy-auction<br/><i>Business Interface</i>"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    ADMIN --> ATT
    ADMIN --> BSTOCK
    ADMIN --> SPRINT

    ATT --> COMMON
    BSTOCK --> COMMON
    SPRINT --> COMMON

    ORDERS --> ATT
    ORDERS --> BSTOCK
    ORDERS --> SPRINT
    ORDERS --> COMMON

    %% 🔵 移动端直连业务服务 (主图中的重要关系)
    FE6 --> ATT
    FE6 --> BSTOCK
    FE6 --> SPRINT

    %% 🟠 访问关系 (Access)
    ATT -.-> REDIS
    ATT -.-> NACOS
    BSTOCK -.-> REDIS
    BSTOCK -.-> NACOS
    SPRINT -.-> REDIS
    SPRINT -.-> NACOS

    %% ArchiMate 3.1 样式
    classDef businessLayer fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class FE6 businessLayer
    class ADMIN,COMMON,ORDERS,ATT,BSTOCK,SPRINT applicationLayer
    class REDIS,NACOS technologyLayer
```

#### 🎯 子图4: Galaxy业务生态

> **覆盖范围**: Galaxy系列业务服务、在线竞价及其依赖关系
> **服务数量**: 7个服务 | **依赖关系**: 14个

```mermaid
graph TD
    subgraph "🔵 应用层 - 核心服务接口"
        GW["🚪 sonic-gateway<br/><i>Application Service</i>"]
        ADMIN["📊 bid-system-admin<br/><i>Application Component</i>"]
        COMMON["🔧 common<br/><i>Application Component</i>"]
    end

    subgraph "🔵 应用层 - Galaxy业务生态"
        SELECT["🎯 galaxy-bid-select<br/><i>Application Component</i><br/>Select竞价服务"]
        ASURION["🛡️ galaxy-bid-asurion<br/><i>Application Component</i><br/>Asurion竞价服务"]
        RENUGO["♻️ galaxy-bid-renugo<br/><i>Application Component</i><br/>Renugo竞价服务"]
        ONLINE["🌐 online-bidding<br/><i>Application Component</i><br/>在线竞价服务"]
    end

    subgraph "🔵 应用层 - 移动端应用"
        FE6["📱 galaxy-auction<br/><i>Business Interface</i>"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i>"]
        MYSQL["🗄️ MySQL<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    ADMIN --> SELECT
    ADMIN --> ASURION
    ADMIN --> RENUGO
    ADMIN --> ONLINE

    SELECT --> COMMON
    SELECT --> GW
    ASURION --> COMMON
    ASURION --> GW
    RENUGO --> COMMON
    RENUGO --> GW
    ONLINE --> ADMIN

    %% 🔵 移动端直连业务服务 (主图中的重要关系)
    FE6 --> SELECT
    FE6 --> ASURION
    FE6 --> ONLINE

    %% 🟠 访问关系 (Access)
    SELECT -.-> REDIS
    SELECT -.-> NACOS
    ASURION -.-> REDIS
    ASURION -.-> NACOS
    RENUGO -.-> REDIS
    RENUGO -.-> NACOS
    ONLINE -.-> REDIS
    ONLINE -.-> MYSQL
    ONLINE -.-> NACOS

    %% ArchiMate 3.1 样式
    classDef businessLayer fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class FE6 businessLayer
    class GW,ADMIN,COMMON,SELECT,ASURION,RENUGO,ONLINE applicationLayer
    class REDIS,MYSQL,NACOS technologyLayer
```

#### 📋 子图5: 订单管理关系

> **覆盖范围**: bid_orders订单服务与其他服务的依赖关系
> **服务数量**: 8个服务 | **依赖关系**: 10个

```mermaid
graph TD
    subgraph "🔵 应用层 - 核心服务"
        COMMON["🔧 common<br/><i>Application Component</i><br/>通用业务服务"]
        ORDERS["📋 bid_orders<br/><i>Application Component</i><br/>订单管理服务"]
        HYLA["📈 hyla<br/><i>Application Component</i><br/>数据分析引擎"]
    end

    subgraph "🔵 应用层 - 供应商业务服务"
        ATT["📡 att-bid-system<br/><i>Application Component</i><br/>AT&T竞价服务"]
        BSTOCK["📦 bstock_bid<br/><i>Application Component</i><br/>B-Stock竞价服务"]
        SPRINT["🏃 sprint_bid<br/><i>Application Component</i><br/>Sprint竞价服务"]
        SELECT["🎯 galaxy-bid-select<br/><i>Application Component</i><br/>Select竞价服务"]
        ASURION["🛡️ galaxy-bid-asurion<br/><i>Application Component</i><br/>Asurion竞价服务"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i>"]
        MYSQL["🗄️ MySQL<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    ORDERS --> COMMON
    ORDERS --> HYLA
    ORDERS --> ATT
    ORDERS --> BSTOCK
    ORDERS --> SPRINT
    ORDERS --> SELECT
    ORDERS --> ASURION

    %% 🟠 访问关系 (Access)
    ORDERS -.-> REDIS
    ORDERS -.-> MYSQL
    ORDERS -.-> NACOS

    %% ArchiMate 3.1 样式
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class COMMON,ORDERS,HYLA,ATT,BSTOCK,SPRINT,SELECT,ASURION applicationLayer
    class REDIS,MYSQL,NACOS technologyLayer
```

#### 📊 子图6: 管理服务关系

> **覆盖范围**: bid-system-admin管理服务与其他服务的依赖关系
> **服务数量**: 12个服务 | **依赖关系**: 14个

```mermaid
graph TD
    subgraph "🔵 应用层 - 网关管理"
        GW["🚪 sonic-gateway<br/><i>Application Service</i><br/>API网关"]
        ADMIN["📊 bid-system-admin<br/><i>Application Component</i><br/>竞价管理中心"]
        COMMON["🔧 common<br/><i>Application Component</i><br/>通用业务服务"]
        SYNC["🔄 syncdata<br/><i>Application Component</i><br/>数据同步服务"]
        MONITOR["📊 sonic-admin<br/><i>Application Component</i><br/>监控管理"]
    end

    subgraph "🔵 应用层 - 供应商业务服务"
        ATT["📡 att-bid-system<br/><i>Application Component</i><br/>AT&T竞价服务"]
        BSTOCK["📦 bstock_bid<br/><i>Application Component</i><br/>B-Stock竞价服务"]
        SPRINT["🏃 sprint_bid<br/><i>Application Component</i><br/>Sprint竞价服务"]
        SELECT["🎯 galaxy-bid-select<br/><i>Application Component</i><br/>Select竞价服务"]
        ASURION["🛡️ galaxy-bid-asurion<br/><i>Application Component</i><br/>Asurion竞价服务"]
        RENUGO["♻️ galaxy-bid-renugo<br/><i>Application Component</i><br/>Renugo竞价服务"]
        ONLINE["🌐 online-bidding<br/><i>Application Component</i><br/>在线竞价服务"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i>"]
        MONGODB["📄 MongoDB<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    GW --> ADMIN
    ADMIN --> COMMON
    ADMIN --> ATT
    ADMIN --> BSTOCK
    ADMIN --> SPRINT
    ADMIN --> SELECT
    ADMIN --> ASURION
    ADMIN --> RENUGO
    ADMIN --> ONLINE
    ONLINE --> ADMIN
    SYNC --> ADMIN
    MONITOR --> ADMIN

    %% 🟠 访问关系 (Access)
    ADMIN -.-> REDIS
    ADMIN -.-> MONGODB
    ADMIN -.-> NACOS

    %% ArchiMate 3.1 样式
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class GW,ADMIN,COMMON,SYNC,MONITOR,ATT,BSTOCK,SPRINT,SELECT,ASURION,RENUGO,ONLINE applicationLayer
    class REDIS,MONGODB,NACOS technologyLayer
```

#### 📈 子图7: 数据服务关系

> **覆盖范围**: docomo数据服务与其他服务的依赖关系
> **服务数量**: 3个服务 | **依赖关系**: 3个

```mermaid
graph TD
    subgraph "🔵 应用层 - 核心服务"
        COMMON["🔧 common<br/><i>Application Component</i><br/>通用业务服务"]
        DOCOMO["📊 docomo<br/><i>Application Component</i><br/>Docomo数据服务"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    DOCOMO --> COMMON

    %% 🟠 访问关系 (Access)
    DOCOMO -.-> REDIS
    DOCOMO -.-> NACOS

    %% ArchiMate 3.1 样式
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class COMMON,DOCOMO applicationLayer
    class REDIS,NACOS technologyLayer
```

#### 🏢 子图8: ERP核心业务层

> **覆盖范围**: galaxy_odoo_erp ERP系统与其他服务的依赖关系
> **服务数量**: 8个服务 | **依赖关系**: 12个

```mermaid
graph TD
    subgraph "🔵 应用层 - ERP核心服务"
        ERP["🏢 galaxy_odoo_erp<br/><i>Application Component</i><br/>企业资源管理系统"]
    end

    subgraph "🔵 应用层 - 业务服务"
        ADMIN["📊 bid-system-admin<br/><i>Application Component</i><br/>投标管理后台"]
        ONLINE["🌐 online-bidding<br/><i>Application Component</i><br/>在线投标服务"]
        AUCTION["📱 galaxy-auction<br/><i>Business Interface</i><br/>移动端拍卖应用"]
    end

    subgraph "🔵 应用层 - 认证服务"
        AUTH["🔐 maxkey<br/><i>Application Service</i><br/>SSO认证服务"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i><br/>缓存/会话存储"]
        RABBITMQ["📮 RabbitMQ<br/><i>Technology Service</i><br/>消息队列"]
        POSTGRESQL["🐘 PostgreSQL<br/><i>Technology Service</i><br/>ERP数据库"]
    end

    %% 🔵 服务关系 (Serving)
    ERP --> ADMIN
    ERP --> ONLINE
    ERP --> AUCTION
    ADMIN --> ERP
    ONLINE --> ERP
    AUCTION --> ERP

    %% 🔵 认证依赖
    ERP --> AUTH

    %% 🟢 流关系 (Flow)
    ERP -.-> RABBITMQ
    RABBITMQ -.-> ERP

    %% 🟠 访问关系 (Access)
    ERP -.-> REDIS
    ERP -.-> POSTGRESQL

    %% ArchiMate 3.1 标准颜色规范
    classDef businessLayer fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#e65100

    class ERP,ADMIN,ONLINE,AUTH applicationLayer
    class AUCTION businessLayer
    class REDIS,RABBITMQ,POSTGRESQL technologyLayer
```

#### ⚡ 子图9: 数据处理层

> **覆盖范围**: 数据处理服务、推送服务、监控管理服务及其依赖关系
> **服务数量**: 9个服务 | **依赖关系**: 25个

```mermaid
graph TD
    subgraph "🔵 应用层 - 数据处理服务"
        OFFER["💰 offerbids<br/><i>Application Component</i>"]
        EXPRESS["⚡ express<br/><i>Application Component</i>"]
        SYNC["🔄 syncdata<br/><i>Application Component</i>"]
        HYLA["📈 hyla<br/><i>Application Component</i>"]
        PUSH1["📤 push-access-service<br/><i>Application Component</i>"]
        PUSH2["📨 push-send-service<br/><i>Application Component</i>"]
    end

    subgraph "🔵 应用层 - 管理监控服务"
        MONITOR["📊 sonic-admin<br/><i>Application Component</i>"]
        SCHEDULER["⏰ xxl-job<br/><i>Application Component</i>"]
    end

    subgraph "🔵 应用层 - 核心服务接口"
        GW["🚪 sonic-gateway<br/><i>Application Service</i>"]
        AUTH["🔐 maxkey<br/><i>Application Service</i>"]
        ADMIN["📊 bid-system-admin<br/><i>Application Component</i>"]
        COMMON["🔧 common<br/><i>Application Component</i>"]
    end

    subgraph "🟠 技术层 - 基础设施"
        REDIS["💾 Redis<br/><i>Technology Service</i>"]
        RABBITMQ["📮 RabbitMQ<br/><i>Technology Service</i>"]
        MYSQL["🗄️ MySQL<br/><i>Technology Service</i>"]
        NACOS["🎯 Nacos<br/><i>Technology Service</i>"]
        ES["🔍 Elasticsearch<br/><i>Technology Service</i>"]
    end

    %% 🔵 服务关系 (Serving)
    OFFER --> EXPRESS
    OFFER --> HYLA
    EXPRESS --> COMMON
    SYNC --> ADMIN
    MONITOR --> GW
    MONITOR --> AUTH
    MONITOR --> ADMIN

    %% 🟢 流关系 (Flow)
    OFFER -.-> RABBITMQ
    COMMON -.-> PUSH1
    PUSH1 -.-> PUSH2
    PUSH1 -.-> RABBITMQ
    SYNC -.-> RABBITMQ
    HYLA -.-> RABBITMQ

    %% 🟠 访问关系 (Access)
    EXPRESS -.-> REDIS
    EXPRESS -.-> NACOS
    SYNC -.-> REDIS
    SYNC -.-> NACOS
    HYLA -.-> REDIS
    HYLA -.-> NACOS
    PUSH1 -.-> REDIS
    PUSH1 -.-> NACOS
    PUSH1 -.-> ES
    PUSH2 -.-> REDIS
    PUSH2 -.-> NACOS
    PUSH2 -.-> ES
    MONITOR -.-> NACOS
    SCHEDULER -.-> NACOS
    SCHEDULER -.-> MYSQL

    %% ArchiMate 3.1 样式
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class OFFER,EXPRESS,SYNC,HYLA,PUSH1,PUSH2,MONITOR,SCHEDULER,GW,AUTH,ADMIN,COMMON applicationLayer
    class REDIS,RABBITMQ,MYSQL,NACOS,ES technologyLayer
```

#### 🔧 子图10: 基础设施层

> **覆盖范围**: 所有基础设施组件及其被依赖关系的汇总视图
> **基础设施数量**: 6个组件 | **被依赖关系**: 26个

```mermaid
graph TD
    subgraph "🟠 技术层 - 配置与服务发现"
        NACOS["🎯 Nacos<br/><i>Technology Service</i><br/>• 配置管理<br/>• 服务发现<br/>• 健康检查"]
    end

    subgraph "🟠 技术层 - 缓存与消息"
        REDIS["💾 Redis集群<br/><i>Technology Service</i><br/>• 业务缓存<br/>• 会话存储<br/>• Pub/Sub消息"]
        RABBITMQ["📮 RabbitMQ<br/><i>Technology Service</i><br/>• 业务消息<br/>• 数据同步<br/>• 事件驱动"]
    end

    subgraph "🟠 技术层 - 数据存储"
        MYSQL["🗄️ MySQL<br/><i>Technology Service</i><br/>• 业务数据<br/>• 用户信息<br/>• 订单数据"]
        POSTGRESQL["🐘 PostgreSQL<br/><i>Technology Service</i><br/>• ERP数据<br/>• 企业资源<br/>• 业务流程"]
        MONGODB["📄 MongoDB<br/><i>Technology Service</i><br/>• 文档存储<br/>• 日志数据"]
        ES["🔍 Elasticsearch<br/><i>Technology Service</i><br/>• 推送消息存储<br/>• 日志分析<br/>• 全文搜索"]
    end

    subgraph "🔵 应用层 - 服务分组"
        FRONTEND["👥 前端应用组<br/>• maxkey-web-app<br/>• maxkey-web-mgt-app<br/>• manage_front<br/>• auction-management-web<br/>• galaxy-auction"]
        AUTH_GATEWAY["🔐 认证网关组<br/>• sso_project<br/>• sonic-gateway<br/>• maxkey"]
        BUSINESS["🏢 业务服务组<br/>• bid-system-admin<br/>• common<br/>• att-bid-system<br/>• bstock_bid<br/>• sprint_bid<br/>• galaxy-bid-*<br/>• online-bidding"]
        DATA["📊 数据服务组<br/>• offerbids<br/>• express<br/>• syncdata<br/>• hyla<br/>• push-access-service<br/>• push-send-service<br/>• bid_orders<br/>• docomo"]
        MANAGEMENT["⚙️ 管理服务组<br/>• sonic-admin<br/>• xxl-job"]
    end

    %% 🟠 Nacos依赖 (访问关系)
    FRONTEND -.-> NACOS
    AUTH_GATEWAY -.-> NACOS
    BUSINESS -.-> NACOS
    DATA -.-> NACOS
    MANAGEMENT -.-> NACOS

    %% 🟠 Redis依赖 (访问关系)
    BUSINESS -.-> REDIS
    DATA -.-> REDIS

    %% 🟢 RabbitMQ依赖 (流关系)
    BUSINESS -.-> RABBITMQ
    DATA -.-> RABBITMQ

    %% 🟠 数据库依赖 (访问关系)
    AUTH_GATEWAY -.-> MYSQL
    BUSINESS -.-> MYSQL
    BUSINESS -.-> MONGODB
    DATA -.-> MYSQL
    DATA -.-> POSTGRESQL
    MANAGEMENT -.-> MYSQL

    %% 🟠 Elasticsearch依赖 (访问关系)
    DATA -.-> ES

    %% ArchiMate 3.1 样式
    classDef applicationLayer fill:#bbdefb,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef technologyLayer fill:#ffcc80,stroke:#f57c00,stroke-width:2px,color:#e65100

    class FRONTEND,AUTH_GATEWAY,BUSINESS,DATA,MANAGEMENT applicationLayer
    class NACOS,REDIS,RABBITMQ,MYSQL,POSTGRESQL,MONGODB,ES technologyLayer
```

### 📋 子图覆盖完整性验证

#### 🎯 完整性保证

**所有子图的服务和依赖关系完全覆盖主图，无遗漏、无重复**：

| 验证维度 | 完整系统拓扑 | 子图1 | 子图2 | 子图3 | 子图4 | 子图5 | 子图6 | 子图7 | 子图8 | 子图9 | 子图10 | 总计验证 |
|---------|-------------|-------|-------|-------|-------|-------|-------|-------|-------|-------|--------|---------|
| **服务数量** | 25个 | 8个 | 4个 | 6个 | 7个 | 8个 | 12个 | 3个 | 8个 | 13个 | 5个分组 | ✅ 覆盖完整 |
| **依赖关系** | 110个 | 15个 | 12个 | 15个 | 14个 | 10个 | 14个 | 3个 | 12个 | 25个 | 26个 | ✅ 覆盖完整 |
| **基础设施** | 6个 | 2个 | 4个 | 2个 | 3个 | 3个 | 3个 | 2个 | 3个 | 5个 | 6个 | ✅ 覆盖完整 |

#### 🔍 子图分工说明

```yaml
子图分工原则:
  子图1 (前端认证层):
    - 覆盖: 所有前端应用 + 认证网关服务
    - 边界: 用户接入到认证完成
    - 接口: 通过网关连接到管理层

  子图2 (核心管理层):
    - 覆盖: 网关 + 认证 + 管理 + 通用服务
    - 边界: 核心管理和通用服务提供
    - 接口: 为业务层提供管理和通用服务

  子图3 (传统供应商业务):
    - 覆盖: AT&T + B-Stock + Sprint传统供应商业务
    - 边界: 传统供应商竞价业务逻辑
    - 接口: 调用管理层服务，被订单服务调用

  子图4 (Galaxy业务生态):
    - 覆盖: Galaxy系列 + 在线竞价业务
    - 边界: Galaxy生态和在线业务逻辑
    - 接口: 调用管理层和网关服务

  子图5 (订单管理关系):
    - 覆盖: bid_orders与所有供应商业务服务的关系
    - 边界: 订单管理的服务依赖
    - 接口: 调用通用服务和各供应商服务

  子图6 (管理服务关系):
    - 覆盖: bid-system-admin与所有业务服务的关系
    - 边界: 管理服务的控制依赖
    - 接口: 管理所有供应商业务服务

  子图7 (数据服务关系):
    - 覆盖: docomo与通用服务的关系
    - 边界: 数据服务的简单依赖
    - 接口: 调用通用服务

  子图8 (ERP核心业务层):
    - 覆盖: galaxy_odoo_erp ERP系统与其他服务的依赖关系
    - 边界: ERP系统的业务集成
    - 接口: 与投标管理、在线投标、移动端的双向集成

  子图9 (数据处理层):
    - 覆盖: 数据处理 + 推送 + 监控管理服务
    - 边界: 数据采集到输出的完整链路
    - 接口: 为业务层提供数据支撑

  子图10 (基础设施层):
    - 覆盖: 所有基础设施组件的被依赖关系
    - 边界: 基础设施服务提供
    - 接口: 为所有应用层提供基础支撑
```

#### 🎨 关系类型一致性

**所有子图都严格遵循主图的ArchiMate 3.1关系类型标准**：

| 关系类型 | Mermaid语法 | 视觉表示 | 在各子图中的应用 |
|---------|-------------|---------|-----------------|
| **🔵 服务关系 (Serving)** | `A --> B` | 实线箭头 | 所有服务间的功能提供关系 |
| **🟢 流关系 (Flow)** | `A -.-> B` + 流关系注释 | 虚线箭头 | 数据、消息、事件的传递 |
| **🟠 访问关系 (Access)** | `A -.-> B` + 访问关系注释 | 虚线箭头 | 对基础设施的访问依赖 |

**实现方式**：
- **语法区分**: 使用 `-->` 表示服务关系，`-.->` 表示流关系和访问关系
- **注释区分**: 通过详细的注释说明区分流关系和访问关系的语义
- **避免索引错误**: 不使用复杂的linkStyle索引，确保图表稳定显示

**各子图关系类型分布**：
- ✅ 子图1 (前端认证层)：主要使用服务关系 + 基础设施访问关系
- ✅ 子图2 (核心管理层)：服务关系 + 访问关系
- ✅ 子图3 (传统供应商业务)：服务关系 + 访问关系
- ✅ 子图4 (Galaxy业务生态)：服务关系 + 访问关系
- ✅ 子图5 (订单管理关系)：主要使用服务关系 + 访问关系
- ✅ 子图6 (管理服务关系)：主要使用服务关系 + 访问关系
- ✅ 子图7 (数据服务关系)：简单的服务关系 + 访问关系
- ✅ 子图8 (数据处理层)：三种关系类型完整应用
- ✅ 子图9 (基础设施层)：主要使用访问关系 + 部分流关系

#### 💡 子图使用建议

1. **架构理解时**：
   - 先看完整系统拓扑了解全貌
   - 按层次逐个查看子图：前端认证层 → 核心管理层 → 业务层(子图3-4) → 关系层(子图5-7) → 数据处理层 → 基础设施层
   - 注意各子图间的接口和边界

2. **问题排查时**：
   - 用户访问问题 → 子图1 (前端认证层)
   - 管理功能问题 → 子图2 (核心管理层) + 子图6 (管理服务关系)
   - 传统供应商业务问题 → 子图3 (传统供应商业务)
   - Galaxy生态业务问题 → 子图4 (Galaxy业务生态)
   - 订单管理问题 → 子图5 (订单管理关系)
   - 数据服务问题 → 子图7 (数据服务关系)
   - 数据处理问题 → 子图8 (数据处理层)
   - 基础设施问题 → 子图9 (基础设施层)

3. **开发协作时**：
   - 前端团队 → 重点关注子图1
   - 平台开发团队 → 重点关注子图2 + 子图6
   - 传统业务开发团队 → 重点关注子图3
   - Galaxy业务开发团队 → 重点关注子图4
   - 订单管理团队 → 重点关注子图5
   - 数据服务团队 → 重点关注子图7
   - 数据处理团队 → 重点关注子图8
   - 运维团队 → 重点关注子图9

4. **架构演进时**：
   - 确保变更在相关子图中得到体现
   - 验证子图间接口的一致性
   - 特别关注关系子图(5-7)的依赖变化
   - 保持完整性验证：所有子图覆盖 = 完整系统拓扑

## 📊 架构统计

### 服务分布
| 层次 | 服务数量 | 主要技术栈 |
|------|---------|-----------|
| **前端层** | 6个 | Vue 2/3, Angular 15, iOS Native |
| **网关层** | 2个 | Spring Cloud Gateway |
| **业务层** | 11个 | Spring Boot, Java 8/17 |
| **数据层** | 5个 | Python, C++, Spring Boot |
| **基础设施** | 7个实例 | Redis, RabbitMQ, MySQL等 |

### 依赖关系统计
| 依赖类型 | 数量 | 占比 |
|---------|------|------|
| HTTP/RPC接口 | 35个 | 35.7% |
| 配置中心依赖 (Nacos) | 24个 | 24.5% |
| Redis基础设施 | 18个 | 18.4% |
| WebSocket | 11个 | 11.2% |
| RabbitMQ | 7个 | 7.1% |
| 数据库 | 3个 | 3.1% |
| **总计** | **98个** | **100%** |

## 🎯 架构特点分析

### 优势
1. **业务隔离性好**: 按供应商维度拆分，故障隔离
2. **技术栈现代化**: Spring Boot微服务，容器化部署
3. **性能处理能力强**: C++引擎处理高频数据
4. **扩展性良好**: 微服务架构支持水平扩展
5. **统一认证**: maxkey提供SSO单点登录

### 挑战
1. **服务依赖复杂**: 89个依赖关系，管理复杂度高
2. **技术栈多样**: Java/Python/C++多技术栈，维护成本高
3. **单点故障风险**: 关键服务缺乏高可用设计
4. **基础设施分散**: Redis实例分散，管理复杂
5. **监控体系不完善**: 缺乏统一的监控和治理

## 🔧 技术债务分析

### 技术债务概览
基于现有架构分析，识别出以下主要技术债务：

### 高优先级技术债务
1. **单点故障风险** - 核心服务缺乏高可用设计
2. **架构分散问题** - Redis实例分散，管理复杂
3. **服务治理缺失** - 缺乏统一的服务间调用管理

### 中优先级技术债务
4. **监控体系不完善** - 缺乏全面的监控和告警
5. **配置管理分散** - 配置变更存在风险

### 技术债务影响
- **开发效率**: 新功能开发周期长
- **运维成本**: 故障排查和恢复时间长
- **系统稳定性**: 存在级联故障风险

**详细的技术债务分析和解决方案请参考**:
- [风险评估报告](RISK_ASSESSMENT.md) - 技术风险详细分析
- [投资建议](../../EXECUTIVE_SUMMARY.md#投资建议与优先级) - 技术债务解决的投资规划

## 🎨 功能分组架构视角

基于对现有24个服务的深入分析，我们提供以下基于功能或目的的分组整合架构视角，以简化复杂的服务依赖关系理解：

### 📋 架构视角总览

| 视角编号 | 中文名称 | 英文名称 | 理论依据 | ArchiMate视图类型 | 目标受众 |
|---------|---------|----------|----------|------------------|----------|
| **视角1** | 业务功能分组视图 | Business Function Grouping View | Domain-Driven Design + TOGAF Business Architecture | Business Layer View | 业务分析师、产品经理 |
| **视角2** | 技术分层分组视图 | Technical Layer Grouping View | Layered Architecture Pattern + TOGAF Application Architecture | Layered View | 架构师、技术负责人 |
| **视角3** | 数据流分组视图 | Data Flow Grouping View | Data Flow Architecture + TOGAF Data Architecture | Information Structure View | 数据架构师、开发团队 |
| **视角4** | 部署分组视图 | Deployment Grouping View | Deployment Architecture Pattern + TOGAF Technology Architecture | Implementation & Migration View | 运维团队、DevOps工程师 |
| **视角5** | 治理分组视图 | Governance Grouping View | Architecture Governance Framework + TOGAF Architecture Governance | Motivation View + Strategy View | 架构委员会、管理层 |

### 🎯 ArchiMate元素使用规范

| 概念类型 | 正确的ArchiMate元素 | Visual Paradigm表示 | 使用场景 |
|---------|-------------------|-------------------|----------|
| **功能域/分层** | `Grouping` | 虚线矩形框 + 标签 | 逻辑分组，不代表具体架构元素 |
| **域内业务服务** | `Business Service` | 实线圆角矩形 + 业务图标 | 具体的业务服务实现 |
| **域内应用服务** | `Application Service` | 实线矩形 + 应用图标 | 具体的应用服务实现 |
| **域内应用组件** | `Application Component` | 实线矩形 + 组件图标 | 具体的应用组件实现 |
| **域内技术服务** | `Technology Service` | 实线矩形 + 技术图标 | 具体的技术服务实现 |

### 🏗️ 架构视角1: 业务功能分组视图 (Business Function Grouping View)

> **设计理念**: 按照业务功能域将服务进行逻辑分组，突出业务价值链
> **理论依据**: Domain-Driven Design (DDD) + TOGAF Business Architecture
> **ArchiMate视图类型**: Business Layer View with Grouping Elements

#### 📋 业务功能分组拓扑

```mermaid
graph TD
    subgraph "🟢 用户接入域 (User Access Domain)"
        UA_GROUP["🎯 用户接入服务组<br/><i>Business Service Group</i><br/>• 前端应用集群 (5个)<br/>• SSO认证中心<br/>• API网关"]
    end

    subgraph "🔵 核心业务域 (Core Business Domain)"
        CB_MGMT["📊 业务管理服务组<br/><i>Application Service Group</i><br/>• bid-system-admin<br/>• common<br/>• sonic-admin"]

        CB_TRADING["💰 交易执行服务组<br/><i>Application Service Group</i><br/>• online-bidding<br/>• offerbids<br/>• bid_orders"]

        CB_ERP["🏢 企业资源管理<br/><i>Application Component</i><br/>• galaxy_odoo_erp<br/>• 统一数据中心"]
    end

    subgraph "🔵 供应商业务域 (Supplier Business Domain)"
        SB_LEGACY["📡 传统供应商服务组<br/><i>Application Service Group</i><br/>• att-bid-system<br/>• bstock_bid<br/>• sprint_bid"]

        SB_GALAXY["🎯 Galaxy生态服务组<br/><i>Application Service Group</i><br/>• galaxy-bid-select<br/>• galaxy-bid-asurion<br/>• galaxy-bid-renugo"]
    end

    subgraph "🔵 数据服务域 (Data Service Domain)"
        DS_PROCESS["⚡ 数据处理服务组<br/><i>Application Service Group</i><br/>• express (C++引擎)<br/>• syncdata<br/>• hyla"]

        DS_PUSH["📨 消息推送服务组<br/><i>Application Service Group</i><br/>• push-access-service<br/>• push-send-service"]

        DS_EXTERNAL["📊 外部数据服务组<br/><i>Application Service Group</i><br/>• docomo<br/>• 第三方数据接入"]
    end

    subgraph "🔵 平台服务域 (Platform Service Domain)"
        PS_SCHEDULE["⏰ 调度管理服务组<br/><i>Application Service Group</i><br/>• xxl-job<br/>• 任务调度中心"]
    end

    subgraph "🟠 基础设施域 (Infrastructure Domain)"
        INFRA_STORAGE["💾 存储基础设施<br/><i>Technology Service Group</i><br/>• Redis集群<br/>• MySQL<br/>• MongoDB<br/>• PostgreSQL"]

        INFRA_COMM["📮 通信基础设施<br/><i>Technology Service Group</i><br/>• RabbitMQ<br/>• Nacos<br/>• Elasticsearch"]
    end

    %% 🔵 业务域间的服务关系
    UA_GROUP --> CB_MGMT
    UA_GROUP --> CB_TRADING
    UA_GROUP --> CB_ERP

    CB_MGMT --> SB_LEGACY
    CB_MGMT --> SB_GALAXY
    CB_MGMT --> CB_ERP

    CB_TRADING --> CB_ERP
    CB_TRADING --> DS_PROCESS

    SB_LEGACY --> CB_MGMT
    SB_GALAXY --> CB_MGMT

    DS_PROCESS --> CB_MGMT
    DS_PROCESS --> DS_PUSH
    DS_EXTERNAL --> CB_MGMT

    PS_SCHEDULE --> CB_MGMT
    PS_SCHEDULE --> DS_PROCESS

    %% 🟢 数据流关系
    CB_ERP -.-> DS_PUSH
    DS_PROCESS -.-> DS_PUSH
    CB_TRADING -.-> DS_PUSH

    %% 🟠 基础设施访问关系
    UA_GROUP -.-> INFRA_STORAGE
    UA_GROUP -.-> INFRA_COMM
    CB_MGMT -.-> INFRA_STORAGE
    CB_MGMT -.-> INFRA_COMM
    CB_TRADING -.-> INFRA_STORAGE
    CB_ERP -.-> INFRA_STORAGE
    CB_ERP -.-> INFRA_COMM
    SB_LEGACY -.-> INFRA_STORAGE
    SB_LEGACY -.-> INFRA_COMM
    SB_GALAXY -.-> INFRA_STORAGE
    SB_GALAXY -.-> INFRA_COMM
    DS_PROCESS -.-> INFRA_STORAGE
    DS_PROCESS -.-> INFRA_COMM
    DS_PUSH -.-> INFRA_STORAGE
    DS_PUSH -.-> INFRA_COMM
    DS_EXTERNAL -.-> INFRA_STORAGE
    DS_EXTERNAL -.-> INFRA_COMM
    PS_SCHEDULE -.-> INFRA_STORAGE
    PS_SCHEDULE -.-> INFRA_COMM

    %% ArchiMate 3.1 标准颜色规范
    classDef businessDomain fill:#c8e6c9,stroke:#2e7d32,stroke-width:3px,color:#1b5e20
    classDef applicationDomain fill:#bbdefb,stroke:#1976d2,stroke-width:3px,color:#0d47a1
    classDef technologyDomain fill:#ffcc80,stroke:#f57c00,stroke-width:3px,color:#e65100

    class UA_GROUP businessDomain
    class CB_MGMT,CB_TRADING,CB_ERP,SB_LEGACY,SB_GALAXY,DS_PROCESS,DS_PUSH,DS_EXTERNAL,PS_SCHEDULE applicationDomain
    class INFRA_STORAGE,INFRA_COMM technologyDomain
```

#### **ArchiMate图例说明 - 业务功能分组视图**

##### **Visual Paradigm ArchiMate元素映射**

| ArchiMate概念 | Visual Paradigm元素 | 颜色编码 | 图形符号 | 在本视图中的应用 |
|---------------|-------------------|---------|----------|-----------------|
| **功能域分组** | `Grouping` | 虚线边框，颜色区分 | 虚线矩形框 + 域标签 | 用户接入域、核心业务域等 - 业务功能的逻辑分组 |
| **🟢 业务层服务** | `Business Service` | 绿色 `#c8e6c9` | 圆角矩形 + 业务图标 | 用户接入服务组 - 具体的业务服务 |
| **🔵 应用层服务** | `Application Service` | 蓝色 `#bbdefb` | 矩形 + 应用图标 | 各业务域内的应用服务 |
| **🔵 应用层组件** | `Application Component` | 蓝色 `#bbdefb` | 矩形 + 组件图标 | ERP系统等大型应用组件 |
| **🟠 技术层服务** | `Technology Service` | 橙色 `#ffcc80` | 矩形 + 技术图标 | 基础设施域内的技术服务 |

##### **ArchiMate 3.1 关系类型**

| 关系样式 | ArchiMate关系 | Visual Paradigm表示 | 含义 | 在本视图中的使用 |
|---------|---------------|-------------------|------|-----------------|
| **🔵 蓝色实线箭头** | Serving Relationship | 实线箭头 `→` | 服务关系 | 业务域间的功能提供关系 |
| **🟢 绿色虚线箭头** | Flow Relationship | 虚线箭头 `⇢` | 流关系 | 数据和信息在域间的流动 |
| **🟠 橙色点线箭头** | Access Relationship | 点线箭头 `⇣` | 访问关系 | 应用域对基础设施域的访问 |

##### **Visual Paradigm绘制指南**

```yaml
Visual Paradigm设置:
  图表类型: ArchiMate Business Layer View

  分组元素设置:
    Grouping (功能域):
      - 形状: 虚线矩形框 (Dashed Rectangle)
      - 填充: 透明或极浅色
      - 边框: 虚线，颜色区分域类型
        * 用户接入域: #2e7d32 (深绿色)
        * 核心业务域: #1976d2 (深蓝色)
        * 供应商业务域: #1976d2 (深蓝色)
        * 数据服务域: #1976d2 (深蓝色)
        * 平台服务域: #1976d2 (深蓝色)
        * 基础设施域: #f57c00 (深橙色)
      - 标签: 域名称位于框顶部

  域内服务元素设置:
    Business Service:
      - 形状: 圆角矩形
      - 填充: #c8e6c9 (浅绿色)
      - 边框: #2e7d32 (深绿色), 2px, 实线
      - 图标: Business Service图标

    Application Service:
      - 形状: 矩形
      - 填充: #bbdefb (浅蓝色)
      - 边框: #1976d2 (深蓝色), 2px, 实线
      - 图标: Application Service图标

    Application Component:
      - 形状: 矩形
      - 填充: #bbdefb (浅蓝色)
      - 边框: #1976d2 (深蓝色), 2px, 实线
      - 图标: Application Component图标

    Technology Service:
      - 形状: 矩形
      - 填充: #ffcc80 (浅橙色)
      - 边框: #f57c00 (深橙色), 2px, 实线
      - 图标: Technology Service图标

  关系设置:
    Serving Relationship:
      - 线型: 实线
      - 颜色: #1976d2 (蓝色)
      - 箭头: 标准箭头

    Flow Relationship:
      - 线型: 虚线 (5px间隔)
      - 颜色: #2e7d32 (绿色)
      - 箭头: 标准箭头

    Access Relationship:
      - 线型: 点线 (2px点, 3px间隔)
      - 颜色: #f57c00 (橙色)
      - 箭头: 标准箭头

  布局原则:
    - 功能域使用Grouping元素作为容器
    - 域内服务使用具体的ArchiMate元素
    - 域间关系连接具体服务，不连接Grouping
    - 保持域的视觉层次清晰
```

#### 📊 业务功能分组说明

| 功能域 | 服务组 | 包含服务 | 核心职责 | 业务价值 |
|--------|--------|----------|----------|----------|
| **🟢 用户接入域** | 用户接入服务组 | 5个前端应用 + sso_project + sonic-gateway + maxkey | 用户界面和认证 | 用户体验统一入口 |
| **🔵 核心业务域** | 业务管理服务组 | bid-system-admin + common + sonic-admin | 业务流程管理 | 核心业务逻辑控制 |
| | 交易执行服务组 | online-bidding + offerbids + bid_orders | 交易处理执行 | 直接业务价值创造 |
| | 企业资源管理 | galaxy_odoo_erp | 统一数据和流程 | 企业数据中心 |
| **🔵 供应商业务域** | 传统供应商服务组 | att-bid-system + bstock_bid + sprint_bid | 传统供应商对接 | 现有业务支撑 |
| | Galaxy生态服务组 | galaxy-bid-select + galaxy-bid-asurion + galaxy-bid-renugo | 新兴供应商对接 | 业务扩展支撑 |
| **🔵 数据服务域** | 数据处理服务组 | express + syncdata + hyla | 数据采集处理 | 数据驱动决策 |
| | 消息推送服务组 | push-access-service + push-send-service | 实时通信 | 用户体验提升 |
| | 外部数据服务组 | docomo + 第三方接入 | 外部数据集成 | 数据丰富度提升 |
| **🔵 平台服务域** | 调度管理服务组 | xxl-job | 任务调度 | 系统自动化 |
| **🟠 基础设施域** | 存储基础设施 | Redis + MySQL + MongoDB + PostgreSQL | 数据存储 | 技术基础支撑 |
| | 通信基础设施 | RabbitMQ + Nacos + Elasticsearch | 服务通信 | 技术基础支撑 |

### 🏗️ 架构视角2: 技术分层分组视图 (Technical Layer Grouping View)

> **设计理念**: 按照技术架构分层原则进行分组，突出技术实现层次
> **理论依据**: Layered Architecture Pattern + TOGAF Application Architecture
> **ArchiMate视图类型**: Layered View with Grouping Elements

#### 📋 技术分层分组拓扑

```mermaid
graph TD
    subgraph "🟢 表现层 (Presentation Layer)"
        PL_WEB["🌐 Web应用集群<br/><i>Business Interface Group</i><br/>• maxkey-web-app (Angular)<br/>• maxkey-web-mgt-app (Angular)<br/>• manage_front (Vue)<br/>• auction-management-web (Vue)"]

        PL_MOBILE["📱 移动应用<br/><i>Business Interface</i><br/>• galaxy-auction (iOS Native)"]

        PL_SSO["🔐 单点登录门户<br/><i>Application Service</i><br/>• sso_project (Vue + OAuth2)"]
    end

    subgraph "🔵 服务层 (Service Layer)"
        SL_GATEWAY["🚪 网关服务层<br/><i>Application Service Group</i><br/>• sonic-gateway (API网关)<br/>• maxkey (认证服务)"]

        SL_BUSINESS["🏢 业务服务层<br/><i>Application Component Group</i><br/>• bid-system-admin (管理中心)<br/>• common (通用服务)<br/>• galaxy_odoo_erp (ERP核心)"]

        SL_DOMAIN["🎯 领域服务层<br/><i>Application Component Group</i><br/>• 6个供应商竞价服务<br/>• online-bidding (在线投标)<br/>• offerbids (报价投标)"]
    end

    subgraph "🔵 集成层 (Integration Layer)"
        IL_DATA["⚡ 数据集成层<br/><i>Application Component Group</i><br/>• express (C++数据引擎)<br/>• syncdata (数据同步)<br/>• hyla (数据采集)<br/>• docomo (外部数据)"]

        IL_MESSAGE["📨 消息集成层<br/><i>Application Component Group</i><br/>• push-access-service<br/>• push-send-service<br/>• bid_orders (订单处理)"]

        IL_PLATFORM["⚙️ 平台集成层<br/><i>Application Component Group</i><br/>• sonic-admin (监控管理)<br/>• xxl-job (任务调度)"]
    end

    subgraph "🟠 数据层 (Data Layer)"
        DL_CACHE["💾 缓存数据层<br/><i>Technology Service Group</i><br/>• Redis集群<br/>• 会话存储<br/>• 业务缓存"]

        DL_PERSIST["🗄️ 持久化数据层<br/><i>Technology Service Group</i><br/>• MySQL (业务数据)<br/>• MongoDB (文档数据)<br/>• PostgreSQL (ERP数据)"]

        DL_SEARCH["🔍 搜索数据层<br/><i>Technology Service</i><br/>• Elasticsearch<br/>• 全文搜索<br/>• 日志分析"]
    end

    subgraph "🟠 基础设施层 (Infrastructure Layer)"
        INF_COMM["📮 通信基础设施<br/><i>Technology Service Group</i><br/>• RabbitMQ (消息队列)<br/>• Nacos (服务发现)"]

        INF_MONITOR["📊 监控基础设施<br/><i>Technology Service Group</i><br/>• 日志收集<br/>• 性能监控<br/>• 健康检查"]
    end

    %% 🔵 层间服务关系
    PL_WEB --> PL_SSO
    PL_MOBILE --> PL_SSO
    PL_SSO --> SL_GATEWAY

    SL_GATEWAY --> SL_BUSINESS
    SL_GATEWAY --> SL_DOMAIN

    SL_BUSINESS --> SL_DOMAIN
    SL_BUSINESS --> IL_DATA
    SL_BUSINESS --> IL_MESSAGE

    SL_DOMAIN --> IL_DATA

    IL_DATA --> IL_MESSAGE
    IL_PLATFORM --> SL_BUSINESS
    IL_PLATFORM --> IL_DATA

    %% 🟢 数据流关系
    SL_BUSINESS -.-> IL_MESSAGE
    IL_DATA -.-> IL_MESSAGE
    IL_MESSAGE -.-> INF_COMM

    %% 🟠 数据访问关系
    SL_GATEWAY -.-> DL_CACHE
    SL_GATEWAY -.-> DL_PERSIST
    SL_BUSINESS -.-> DL_CACHE
    SL_BUSINESS -.-> DL_PERSIST
    SL_DOMAIN -.-> DL_CACHE
    SL_DOMAIN -.-> DL_PERSIST
    IL_DATA -.-> DL_CACHE
    IL_DATA -.-> DL_PERSIST
    IL_MESSAGE -.-> DL_CACHE
    IL_MESSAGE -.-> DL_SEARCH
    IL_PLATFORM -.-> DL_CACHE
    IL_PLATFORM -.-> DL_PERSIST

    %% 🟠 基础设施访问关系
    SL_GATEWAY -.-> INF_COMM
    SL_BUSINESS -.-> INF_COMM
    SL_DOMAIN -.-> INF_COMM
    IL_DATA -.-> INF_COMM
    IL_MESSAGE -.-> INF_COMM
    IL_PLATFORM -.-> INF_COMM
    IL_PLATFORM -.-> INF_MONITOR

    %% ArchiMate 3.1 标准颜色规范
    classDef presentationLayer fill:#c8e6c9,stroke:#2e7d32,stroke-width:3px,color:#1b5e20
    classDef serviceLayer fill:#bbdefb,stroke:#1976d2,stroke-width:3px,color:#0d47a1
    classDef integrationLayer fill:#e1bee7,stroke:#7b1fa2,stroke-width:3px,color:#4a148c
    classDef dataLayer fill:#ffcc80,stroke:#f57c00,stroke-width:3px,color:#e65100
    classDef infrastructureLayer fill:#ffcdd2,stroke:#d32f2f,stroke-width:3px,color:#b71c1c

    class PL_WEB,PL_MOBILE,PL_SSO presentationLayer
    class SL_GATEWAY,SL_BUSINESS,SL_DOMAIN serviceLayer
    class IL_DATA,IL_MESSAGE,IL_PLATFORM integrationLayer
    class DL_CACHE,DL_PERSIST,DL_SEARCH dataLayer
    class INF_COMM,INF_MONITOR infrastructureLayer
```

#### **ArchiMate图例说明 - 技术分层分组视图**

##### **Visual Paradigm ArchiMate元素映射**

| ArchiMate层次 | Visual Paradigm元素 | 颜色编码 | 图形符号 | 在本视图中的应用 |
|---------------|-------------------|---------|----------|-----------------|
| **🟢 业务层** | Business Interface Group | 绿色 `#c8e6c9` | 圆角矩形 + 接口图标 | 表现层 - 用户交互界面集合 |
| **🔵 应用层** | Application Service Group | 蓝色 `#bbdefb` | 矩形 + 服务图标 | 服务层 - 应用服务的逻辑分组 |
| **🔵 应用层** | Application Component Group | 蓝色 `#bbdefb` | 矩形 + 组件图标 | 服务层和集成层 - 应用组件分组 |
| **🔵 应用层** | Application Component Group | 紫色 `#e1bee7` | 矩形 + 集成图标 | 集成层 - 集成组件的特殊标识 |
| **🟠 技术层** | Technology Service Group | 橙色 `#ffcc80` | 矩形 + 数据图标 | 数据层 - 数据技术服务分组 |
| **🟠 技术层** | Technology Service Group | 红色 `#ffcdd2` | 矩形 + 基础设施图标 | 基础设施层 - 基础技术服务分组 |

##### **ArchiMate 3.1 关系类型**

| 关系样式 | ArchiMate关系 | Visual Paradigm表示 | 含义 | 在本视图中的使用 |
|---------|---------------|-------------------|------|-----------------|
| **🔵 蓝色实线箭头** | Serving Relationship | 实线箭头 `→` | 服务关系 | 层间的服务提供关系 |
| **🟢 绿色虚线箭头** | Flow Relationship | 虚线箭头 `⇢` | 流关系 | 数据和消息在层间的流动 |
| **🟠 橙色点线箭头** | Access Relationship | 点线箭头 `⇣` | 访问关系 | 上层对下层技术资源的访问 |

##### **Visual Paradigm绘制指南**

```yaml
Visual Paradigm设置:
  图表类型: ArchiMate Layered View

  层次布局:
    - 表现层 (顶部)
    - 服务层
    - 集成层 (特殊颜色标识)
    - 数据层
    - 基础设施层 (底部)

  元素设置:
    Business Interface Group:
      - 形状: 圆角矩形
      - 填充: #c8e6c9 (浅绿色)
      - 边框: #2e7d32 (深绿色), 3px
      - 图标: Business Interface图标

    Application Service Group:
      - 形状: 矩形
      - 填充: #bbdefb (浅蓝色)
      - 边框: #1976d2 (深蓝色), 3px
      - 图标: Application Service图标

    Integration Layer (特殊):
      - 形状: 矩形
      - 填充: #e1bee7 (浅紫色)
      - 边框: #7b1fa2 (深紫色), 3px
      - 图标: Integration图标

    Technology Service Group (Data):
      - 形状: 矩形
      - 填充: #ffcc80 (浅橙色)
      - 边框: #f57c00 (深橙色), 3px
      - 图标: Database图标

    Technology Service Group (Infrastructure):
      - 形状: 矩形
      - 填充: #ffcdd2 (浅红色)
      - 边框: #d32f2f (深红色), 3px
      - 图标: Infrastructure图标

  关系设置:
    层间依赖: 垂直方向，上层依赖下层
    同层关系: 水平方向，协作关系
```

#### 📊 技术分层分组说明

| 技术层 | 服务组 | 技术栈 | 核心职责 | 架构价值 |
|--------|--------|--------|----------|----------|
| **🟢 表现层** | Web应用集群 | Angular 15, Vue 2/3 | 用户界面展示 | 用户体验一致性 |
| | 移动应用 | iOS Native | 移动端交互 | 移动业务支撑 |
| | 单点登录门户 | Vue 3 + OAuth2 | 统一认证入口 | 安全性保障 |
| **🔵 服务层** | 网关服务层 | Spring Cloud Gateway | API路由和认证 | 服务统一入口 |
| | 业务服务层 | Spring Boot + Odoo | 核心业务逻辑 | 业务价值实现 |
| | 领域服务层 | Spring Boot | 领域特定逻辑 | 业务隔离和扩展 |
| **🔵 集成层** | 数据集成层 | C++, Python, Java | 数据处理和同步 | 数据一致性 |
| | 消息集成层 | Spring Boot | 异步消息处理 | 系统解耦 |
| | 平台集成层 | Spring Boot Admin | 系统管理和调度 | 运维自动化 |
| **🟠 数据层** | 缓存数据层 | Redis | 高速数据访问 | 性能优化 |
| | 持久化数据层 | MySQL, MongoDB, PostgreSQL | 数据持久化存储 | 数据可靠性 |
| | 搜索数据层 | Elasticsearch | 全文搜索和分析 | 数据查询能力 |
| **🟠 基础设施层** | 通信基础设施 | RabbitMQ, Nacos | 服务间通信 | 系统连通性 |
| | 监控基础设施 | 监控工具链 | 系统监控和运维 | 系统可观测性 |

### 🏗️ 架构视角3: 数据流分组视图 (Data Flow Grouping View)

> **设计理念**: 按照数据流向和处理链路进行分组，突出数据驱动架构
> **理论依据**: Data Flow Architecture + TOGAF Data Architecture
> **ArchiMate视图类型**: Information Structure View with Grouping Elements

#### 📋 数据流分组拓扑

```mermaid
graph TD
    subgraph "🟢 数据源层 (Data Source Layer)"
        DS_USER["👥 用户数据源<br/><i>Business Actor Group</i><br/>• 前端用户输入<br/>• 移动端操作<br/>• 管理员配置"]

        DS_EXTERNAL["🌐 外部数据源<br/><i>Business Actor Group</i><br/>• 供应商系统<br/>• 第三方API<br/>• 外部数据库"]

        DS_SYSTEM["⚙️ 系统数据源<br/><i>Application Event Group</i><br/>• 定时任务<br/>• 系统事件<br/>• 监控数据"]
    end

    subgraph "🔵 数据接入层 (Data Ingestion Layer)"
        DI_GATEWAY["🚪 数据网关<br/><i>Application Service Group</i><br/>• sonic-gateway<br/>• maxkey<br/>• API接入控制"]

        DI_COLLECT["📥 数据采集<br/><i>Application Component Group</i><br/>• hyla (外部数据采集)<br/>• docomo (特定数据源)<br/>• express (实时数据接入)"]

        DI_AUTH["🔐 认证数据处理<br/><i>Application Component</i><br/>• sso_project<br/>• 用户身份数据"]
    end

    subgraph "🔵 数据处理层 (Data Processing Layer)"
        DP_CORE["🏢 核心数据处理<br/><i>Application Component Group</i><br/>• galaxy_odoo_erp (数据中心)<br/>• bid-system-admin (管理数据)<br/>• common (通用数据服务)"]

        DP_BUSINESS["💼 业务数据处理<br/><i>Application Component Group</i><br/>• online-bidding (投标数据)<br/>• offerbids (报价数据)<br/>• bid_orders (订单数据)"]

        DP_SUPPLIER["🏭 供应商数据处理<br/><i>Application Component Group</i><br/>• 6个供应商竞价服务<br/>• 供应商特定数据逻辑"]
    end

    subgraph "🔵 数据同步层 (Data Synchronization Layer)"
        DS_SYNC["🔄 数据同步服务<br/><i>Application Component Group</i><br/>• syncdata (数据同步)<br/>• express (实时同步)<br/>• 数据一致性保障"]

        DS_MESSAGE["📨 消息数据处理<br/><i>Application Component Group</i><br/>• push-access-service<br/>• push-send-service<br/>• 实时消息数据"]

        DS_SCHEDULE["⏰ 调度数据处理<br/><i>Application Component</i><br/>• xxl-job<br/>• 批处理数据任务"]
    end

    subgraph "🔵 数据监控层 (Data Monitoring Layer)"
        DM_MONITOR["📊 数据监控<br/><i>Application Component</i><br/>• sonic-admin<br/>• 数据质量监控<br/>• 系统健康数据"]
    end

    subgraph "🟠 数据存储层 (Data Storage Layer)"
        DST_CACHE["💾 缓存存储<br/><i>Technology Service Group</i><br/>• Redis集群<br/>• 热数据缓存<br/>• 会话数据"]

        DST_OLTP["🗄️ 事务存储<br/><i>Technology Service Group</i><br/>• MySQL (业务事务)<br/>• MongoDB (文档数据)<br/>• PostgreSQL (ERP数据)"]

        DST_SEARCH["🔍 搜索存储<br/><i>Technology Service</i><br/>• Elasticsearch<br/>• 索引数据<br/>• 日志数据"]
    end

    subgraph "🟠 数据通信层 (Data Communication Layer)"
        DC_QUEUE["📮 消息队列<br/><i>Technology Service</i><br/>• RabbitMQ<br/>• 异步数据传输<br/>• 事件驱动数据流"]

        DC_CONFIG["🎯 配置数据<br/><i>Technology Service</i><br/>• Nacos<br/>• 配置数据管理<br/>• 服务发现数据"]
    end

    %% 🔵 数据流向关系
    DS_USER --> DI_GATEWAY
    DS_USER --> DI_AUTH
    DS_EXTERNAL --> DI_COLLECT
    DS_SYSTEM --> DS_SCHEDULE

    DI_GATEWAY --> DP_CORE
    DI_GATEWAY --> DP_BUSINESS
    DI_COLLECT --> DP_CORE
    DI_AUTH --> DP_CORE

    DP_CORE --> DP_BUSINESS
    DP_CORE --> DP_SUPPLIER
    DP_BUSINESS --> DS_SYNC
    DP_SUPPLIER --> DS_SYNC

    DS_SYNC --> DP_CORE
    DS_SYNC --> DS_MESSAGE
    DS_SCHEDULE --> DP_CORE
    DS_SCHEDULE --> DS_SYNC

    DM_MONITOR --> DP_CORE
    DM_MONITOR --> DS_SYNC

    %% 🟢 数据流关系 (异步数据传输)
    DP_CORE -.-> DS_MESSAGE
    DP_BUSINESS -.-> DS_MESSAGE
    DS_SYNC -.-> DC_QUEUE
    DS_MESSAGE -.-> DC_QUEUE

    %% 🟠 数据存储访问关系
    DI_GATEWAY -.-> DST_CACHE
    DI_AUTH -.-> DST_CACHE
    DP_CORE -.-> DST_CACHE
    DP_CORE -.-> DST_OLTP
    DP_BUSINESS -.-> DST_CACHE
    DP_BUSINESS -.-> DST_OLTP
    DP_SUPPLIER -.-> DST_CACHE
    DP_SUPPLIER -.-> DST_OLTP
    DS_SYNC -.-> DST_CACHE
    DS_MESSAGE -.-> DST_CACHE
    DS_MESSAGE -.-> DST_SEARCH
    DS_SCHEDULE -.-> DST_OLTP
    DM_MONITOR -.-> DST_CACHE

    %% 🟠 配置和通信访问关系
    DI_GATEWAY -.-> DC_CONFIG
    DP_CORE -.-> DC_CONFIG
    DP_BUSINESS -.-> DC_CONFIG
    DP_SUPPLIER -.-> DC_CONFIG
    DS_SYNC -.-> DC_CONFIG
    DS_MESSAGE -.-> DC_CONFIG
    DS_SCHEDULE -.-> DC_CONFIG
    DM_MONITOR -.-> DC_CONFIG

    %% ArchiMate 3.1 标准颜色规范
    classDef dataSourceLayer fill:#c8e6c9,stroke:#2e7d32,stroke-width:3px,color:#1b5e20
    classDef dataIngestionLayer fill:#bbdefb,stroke:#1976d2,stroke-width:3px,color:#0d47a1
    classDef dataProcessingLayer fill:#e3f2fd,stroke:#0277bd,stroke-width:3px,color:#01579b
    classDef dataSyncLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c
    classDef dataMonitoringLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#1b5e20
    classDef dataStorageLayer fill:#ffcc80,stroke:#f57c00,stroke-width:3px,color:#e65100
    classDef dataCommunicationLayer fill:#ffcdd2,stroke:#d32f2f,stroke-width:3px,color:#b71c1c

    class DS_USER,DS_EXTERNAL,DS_SYSTEM dataSourceLayer
    class DI_GATEWAY,DI_COLLECT,DI_AUTH dataIngestionLayer
    class DP_CORE,DP_BUSINESS,DP_SUPPLIER dataProcessingLayer
    class DS_SYNC,DS_MESSAGE,DS_SCHEDULE dataSyncLayer
    class DM_MONITOR dataMonitoringLayer
    class DST_CACHE,DST_OLTP,DST_SEARCH dataStorageLayer
    class DC_QUEUE,DC_CONFIG dataCommunicationLayer
```

#### **ArchiMate图例说明 - 数据流分组视图**

##### **Visual Paradigm ArchiMate元素映射**

| ArchiMate层次 | Visual Paradigm元素 | 颜色编码 | 图形符号 | 在本视图中的应用 |
|---------------|-------------------|---------|----------|-----------------|
| **🟢 业务层** | Business Actor Group | 绿色 `#c8e6c9` | 圆角矩形 + 角色图标 | 数据源层 - 数据产生的业务角色 |
| **🔵 应用层** | Application Event Group | 绿色 `#c8e6c9` | 矩形 + 事件图标 | 系统数据源 - 系统产生的事件数据 |
| **🔵 应用层** | Application Service Group | 蓝色 `#bbdefb` | 矩形 + 服务图标 | 数据接入层 - 数据接入服务分组 |
| **🔵 应用层** | Application Component Group | 浅蓝色 `#e3f2fd` | 矩形 + 组件图标 | 数据处理层 - 数据处理组件分组 |
| **🔵 应用层** | Application Component Group | 紫色 `#f3e5f5` | 矩形 + 同步图标 | 数据同步层 - 数据同步组件分组 |
| **🔵 应用层** | Application Component | 浅绿色 `#e8f5e8` | 矩形 + 监控图标 | 数据监控层 - 数据监控组件 |
| **🟠 技术层** | Technology Service Group | 橙色 `#ffcc80` | 矩形 + 存储图标 | 数据存储层 - 存储技术服务分组 |
| **🟠 技术层** | Technology Service Group | 红色 `#ffcdd2` | 矩形 + 通信图标 | 数据通信层 - 通信技术服务分组 |

##### **ArchiMate 3.1 关系类型**

| 关系样式 | ArchiMate关系 | Visual Paradigm表示 | 含义 | 在本视图中的使用 |
|---------|---------------|-------------------|------|-----------------|
| **🔵 蓝色实线箭头** | Serving Relationship | 实线箭头 `→` | 服务关系 | 数据处理链路中的服务提供关系 |
| **🟢 绿色虚线箭头** | Flow Relationship | 虚线箭头 `⇢` | 流关系 | 数据在处理链路中的流动 |
| **🟠 橙色点线箭头** | Access Relationship | 点线箭头 `⇣` | 访问关系 | 数据处理层对存储和通信层的访问 |

##### **Visual Paradigm绘制指南**

```yaml
Visual Paradigm设置:
  图表类型: ArchiMate Information Structure View

  数据流布局:
    - 数据源层 (顶部) - 数据产生
    - 数据接入层 - 数据获取
    - 数据处理层 - 数据加工
    - 数据同步层 - 数据同步
    - 数据监控层 - 数据监控
    - 数据存储层 (底部左) - 数据存储
    - 数据通信层 (底部右) - 数据传输

  元素设置:
    Business Actor Group (数据源):
      - 形状: 圆角矩形
      - 填充: #c8e6c9 (浅绿色)
      - 边框: #2e7d32 (深绿色), 3px
      - 图标: Business Actor图标

    Application Service Group (接入层):
      - 形状: 矩形
      - 填充: #bbdefb (浅蓝色)
      - 边框: #1976d2 (深蓝色), 3px
      - 图标: Application Service图标

    Application Component Group (处理层):
      - 形状: 矩形
      - 填充: #e3f2fd (极浅蓝色)
      - 边框: #0277bd (中蓝色), 3px
      - 图标: Application Component图标

    Application Component Group (同步层):
      - 形状: 矩形
      - 填充: #f3e5f5 (浅紫色)
      - 边框: #7b1fa2 (深紫色), 3px
      - 图标: Synchronization图标

    Technology Service Group (存储层):
      - 形状: 矩形
      - 填充: #ffcc80 (浅橙色)
      - 边框: #f57c00 (深橙色), 3px
      - 图标: Database图标

    Technology Service Group (通信层):
      - 形状: 矩形
      - 填充: #ffcdd2 (浅红色)
      - 边框: #d32f2f (深红色), 3px
      - 图标: Communication图标

  关系设置:
    数据流向: 从上到下，从左到右
    处理链路: 顺序连接
    存储访问: 点线连接到存储层
```

#### 📊 数据流分组说明

| 数据层 | 服务组 | 数据类型 | 核心职责 | 数据价值 |
|--------|--------|----------|----------|----------|
| **🟢 数据源层** | 用户数据源 | 用户输入、操作数据 | 业务数据产生 | 业务驱动源头 |
| | 外部数据源 | 第三方、供应商数据 | 外部数据集成 | 数据丰富度 |
| | 系统数据源 | 系统事件、监控数据 | 系统状态数据 | 运维决策支撑 |
| **🔵 数据接入层** | 数据网关 | API请求数据 | 数据接入控制 | 数据安全保障 |
| | 数据采集 | 外部采集数据 | 数据获取和清洗 | 数据质量保证 |
| | 认证数据处理 | 用户身份数据 | 身份验证数据 | 安全性保障 |
| **🔵 数据处理层** | 核心数据处理 | 主业务数据 | 核心业务逻辑 | 核心业务价值 |
| | 业务数据处理 | 交易、订单数据 | 业务执行数据 | 直接业务价值 |
| | 供应商数据处理 | 供应商特定数据 | 供应商业务数据 | 业务扩展价值 |
| **🔵 数据同步层** | 数据同步服务 | 同步、一致性数据 | 数据一致性保障 | 数据可靠性 |
| | 消息数据处理 | 实时消息数据 | 实时通信数据 | 用户体验提升 |
| | 调度数据处理 | 批处理任务数据 | 定时数据处理 | 系统自动化 |
| **🔵 数据监控层** | 数据监控 | 监控、健康数据 | 系统状态监控 | 运维效率提升 |
| **🟠 数据存储层** | 缓存存储 | 热数据、会话数据 | 高速数据访问 | 性能优化 |
| | 事务存储 | 持久化业务数据 | 数据持久化 | 数据可靠性 |
| | 搜索存储 | 索引、日志数据 | 数据查询分析 | 数据洞察能力 |
| **🟠 数据通信层** | 消息队列 | 异步消息数据 | 异步数据传输 | 系统解耦 |
| | 配置数据 | 配置、发现数据 | 系统配置管理 | 系统灵活性 |

### 🏗️ 架构视角4: 部署分组视图 (Deployment Grouping View)

> **设计理念**: 按照部署和运维角度进行分组，突出运维管理便利性
> **理论依据**: Deployment Architecture Pattern + TOGAF Technology Architecture
> **ArchiMate视图类型**: Implementation & Migration View with Grouping Elements

#### 📋 部署分组拓扑

```mermaid
graph TD
    subgraph "🟢 前端部署组 (Frontend Deployment Group)"
        FD_WEB["🌐 Web前端集群<br/><i>Deployment Unit</i><br/>• Nginx + 4个Web应用<br/>• CDN + 静态资源<br/>• 负载均衡"]

        FD_MOBILE["📱 移动端部署<br/><i>Deployment Unit</i><br/>• iOS应用商店<br/>• 移动端更新机制<br/>• 推送服务配置"]
    end

    subgraph "🔵 网关部署组 (Gateway Deployment Group)"
        GD_GATEWAY["🚪 API网关集群<br/><i>Deployment Unit</i><br/>• sonic-gateway (多实例)<br/>• 负载均衡<br/>• SSL终端"]

        GD_AUTH["🔐 认证服务集群<br/><i>Deployment Unit</i><br/>• maxkey (主从)<br/>• sso_project<br/>• 会话共享"]
    end

    subgraph "🔵 核心业务部署组 (Core Business Deployment Group)"
        CBD_MGMT["📊 管理服务集群<br/><i>Deployment Unit</i><br/>• bid-system-admin<br/>• common<br/>• 业务数据库连接池"]

        CBD_ERP["🏢 ERP服务部署<br/><i>Deployment Unit</i><br/>• galaxy_odoo_erp<br/>• PostgreSQL专用<br/>• 独立资源池"]

        CBD_TRADING["💰 交易服务集群<br/><i>Deployment Unit</i><br/>• online-bidding<br/>• offerbids<br/>• bid_orders"]
    end

    subgraph "🔵 供应商服务部署组 (Supplier Service Deployment Group)"
        SSD_LEGACY["📡 传统供应商集群<br/><i>Deployment Unit</i><br/>• att-bid-system<br/>• bstock_bid<br/>• sprint_bid"]

        SSD_GALAXY["🎯 Galaxy服务集群<br/><i>Deployment Unit</i><br/>• galaxy-bid-select<br/>• galaxy-bid-asurion<br/>• galaxy-bid-renugo"]
    end

    subgraph "🔵 数据服务部署组 (Data Service Deployment Group)"
        DSD_PROCESS["⚡ 数据处理集群<br/><i>Deployment Unit</i><br/>• express (C++优化)<br/>• syncdata<br/>• hyla<br/>• docomo"]

        DSD_MESSAGE["📨 消息服务集群<br/><i>Deployment Unit</i><br/>• push-access-service<br/>• push-send-service<br/>• 消息队列连接"]
    end

    subgraph "🔵 平台服务部署组 (Platform Service Deployment Group)"
        PSD_MONITOR["📊 监控管理集群<br/><i>Deployment Unit</i><br/>• sonic-admin<br/>• 监控仪表板<br/>• 告警服务"]

        PSD_SCHEDULE["⏰ 调度服务部署<br/><i>Deployment Unit</i><br/>• xxl-job<br/>• 任务调度中心<br/>• 定时任务管理"]
    end

    subgraph "🟠 数据库部署组 (Database Deployment Group)"
        DBD_CACHE["💾 缓存集群<br/><i>Infrastructure Deployment</i><br/>• Redis Cluster (3主3从)<br/>• 高可用配置<br/>• 数据分片"]

        DBD_RDBMS["🗄️ 关系数据库集群<br/><i>Infrastructure Deployment</i><br/>• MySQL (主从复制)<br/>• PostgreSQL (ERP专用)<br/>• 备份策略"]

        DBD_NOSQL["📄 NoSQL集群<br/><i>Infrastructure Deployment</i><br/>• MongoDB (副本集)<br/>• Elasticsearch (集群)<br/>• 数据备份"]
    end

    subgraph "🟠 中间件部署组 (Middleware Deployment Group)"
        MWD_MESSAGE["📮 消息中间件<br/><i>Infrastructure Deployment</i><br/>• RabbitMQ (集群)<br/>• 消息持久化<br/>• 高可用配置"]

        MWD_CONFIG["🎯 配置中心<br/><i>Infrastructure Deployment</i><br/>• Nacos (集群)<br/>• 配置管理<br/>• 服务发现"]
    end

    %% 🔵 部署依赖关系
    FD_WEB --> GD_GATEWAY
    FD_MOBILE --> GD_GATEWAY
    FD_WEB --> GD_AUTH
    FD_MOBILE --> GD_AUTH

    GD_GATEWAY --> CBD_MGMT
    GD_GATEWAY --> CBD_ERP
    GD_GATEWAY --> CBD_TRADING
    GD_AUTH --> CBD_MGMT

    CBD_MGMT --> SSD_LEGACY
    CBD_MGMT --> SSD_GALAXY
    CBD_MGMT --> DSD_PROCESS
    CBD_ERP --> CBD_TRADING
    CBD_TRADING --> DSD_PROCESS

    SSD_LEGACY --> CBD_MGMT
    SSD_GALAXY --> CBD_MGMT
    DSD_PROCESS --> DSD_MESSAGE

    PSD_MONITOR --> CBD_MGMT
    PSD_MONITOR --> GD_GATEWAY
    PSD_SCHEDULE --> CBD_MGMT
    PSD_SCHEDULE --> DSD_PROCESS

    %% 🟢 数据流关系
    CBD_ERP -.-> DSD_MESSAGE
    DSD_PROCESS -.-> DSD_MESSAGE
    CBD_TRADING -.-> DSD_MESSAGE

    %% 🟠 基础设施依赖关系
    GD_GATEWAY -.-> DBD_CACHE
    GD_GATEWAY -.-> MWD_CONFIG
    GD_AUTH -.-> DBD_CACHE
    GD_AUTH -.-> DBD_RDBMS
    CBD_MGMT -.-> DBD_CACHE
    CBD_MGMT -.-> DBD_NOSQL
    CBD_MGMT -.-> MWD_CONFIG
    CBD_ERP -.-> DBD_CACHE
    CBD_ERP -.-> DBD_RDBMS
    CBD_ERP -.-> MWD_MESSAGE
    CBD_TRADING -.-> DBD_CACHE
    CBD_TRADING -.-> DBD_RDBMS
    SSD_LEGACY -.-> DBD_CACHE
    SSD_LEGACY -.-> MWD_CONFIG
    SSD_GALAXY -.-> DBD_CACHE
    SSD_GALAXY -.-> MWD_CONFIG
    DSD_PROCESS -.-> DBD_CACHE
    DSD_PROCESS -.-> MWD_CONFIG
    DSD_PROCESS -.-> MWD_MESSAGE
    DSD_MESSAGE -.-> DBD_CACHE
    DSD_MESSAGE -.-> DBD_NOSQL
    DSD_MESSAGE -.-> MWD_MESSAGE
    PSD_MONITOR -.-> MWD_CONFIG
    PSD_SCHEDULE -.-> DBD_RDBMS
    PSD_SCHEDULE -.-> MWD_CONFIG

    %% ArchiMate 3.1 标准颜色规范
    classDef frontendDeployment fill:#c8e6c9,stroke:#2e7d32,stroke-width:3px,color:#1b5e20
    classDef gatewayDeployment fill:#bbdefb,stroke:#1976d2,stroke-width:3px,color:#0d47a1
    classDef businessDeployment fill:#e3f2fd,stroke:#0277bd,stroke-width:3px,color:#01579b
    classDef supplierDeployment fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c
    classDef dataDeployment fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#1b5e20
    classDef platformDeployment fill:#fff3e0,stroke:#ff8f00,stroke-width:3px,color:#e65100
    classDef databaseDeployment fill:#ffcc80,stroke:#f57c00,stroke-width:3px,color:#e65100
    classDef middlewareDeployment fill:#ffcdd2,stroke:#d32f2f,stroke-width:3px,color:#b71c1c

    class FD_WEB,FD_MOBILE frontendDeployment
    class GD_GATEWAY,GD_AUTH gatewayDeployment
    class CBD_MGMT,CBD_ERP,CBD_TRADING businessDeployment
    class SSD_LEGACY,SSD_GALAXY supplierDeployment
    class DSD_PROCESS,DSD_MESSAGE dataDeployment
    class PSD_MONITOR,PSD_SCHEDULE platformDeployment
    class DBD_CACHE,DBD_RDBMS,DBD_NOSQL databaseDeployment
    class MWD_MESSAGE,MWD_CONFIG middlewareDeployment
```

#### **ArchiMate图例说明 - 部署分组视图**

##### **Visual Paradigm ArchiMate元素映射**

| ArchiMate层次 | Visual Paradigm元素 | 颜色编码 | 图形符号 | 在本视图中的应用 |
|---------------|-------------------|---------|----------|-----------------|
| **🟢 业务层** | Deployment Unit | 绿色 `#c8e6c9` | 立体矩形 + 部署图标 | 前端部署组 - 面向用户的部署单元 |
| **🔵 应用层** | Deployment Unit | 蓝色 `#bbdefb` | 立体矩形 + 应用图标 | 网关部署组 - 应用网关部署单元 |
| **🔵 应用层** | Deployment Unit | 浅蓝色 `#e3f2fd` | 立体矩形 + 业务图标 | 核心业务部署组 - 业务应用部署单元 |
| **🔵 应用层** | Deployment Unit | 紫色 `#f3e5f5` | 立体矩形 + 供应商图标 | 供应商服务部署组 - 供应商应用部署单元 |
| **🔵 应用层** | Deployment Unit | 浅绿色 `#e8f5e8` | 立体矩形 + 数据图标 | 数据服务部署组 - 数据应用部署单元 |
| **🔵 应用层** | Deployment Unit | 浅橙色 `#fff3e0` | 立体矩形 + 平台图标 | 平台服务部署组 - 平台应用部署单元 |
| **🟠 技术层** | Infrastructure Deployment | 橙色 `#ffcc80` | 立体矩形 + 数据库图标 | 数据库部署组 - 数据库基础设施部署 |
| **🟠 技术层** | Infrastructure Deployment | 红色 `#ffcdd2` | 立体矩形 + 中间件图标 | 中间件部署组 - 中间件基础设施部署 |

##### **ArchiMate 3.1 关系类型**

| 关系样式 | ArchiMate关系 | Visual Paradigm表示 | 含义 | 在本视图中的使用 |
|---------|---------------|-------------------|------|-----------------|
| **🔵 蓝色实线箭头** | Serving Relationship | 实线箭头 `→` | 服务关系 | 部署单元间的服务依赖关系 |
| **🟢 绿色虚线箭头** | Flow Relationship | 虚线箭头 `⇢` | 流关系 | 部署单元间的数据流动关系 |
| **🟠 橙色点线箭头** | Access Relationship | 点线箭头 `⇣` | 访问关系 | 应用部署对基础设施部署的访问 |

##### **Visual Paradigm绘制指南**

```yaml
Visual Paradigm设置:
  图表类型: ArchiMate Implementation & Migration View

  部署层次布局:
    - 前端部署组 (顶部) - 用户接入层
    - 网关部署组 - 接入控制层
    - 核心业务部署组 - 业务逻辑层
    - 供应商服务部署组 - 业务扩展层
    - 数据服务部署组 - 数据处理层
    - 平台服务部署组 - 平台管理层
    - 数据库部署组 (底部左) - 数据存储层
    - 中间件部署组 (底部右) - 通信支撑层

  元素设置:
    Deployment Unit (前端):
      - 形状: 立体矩形 (3D效果)
      - 填充: #c8e6c9 (浅绿色)
      - 边框: #2e7d32 (深绿色), 3px
      - 图标: Web/Mobile Deployment图标

    Deployment Unit (网关):
      - 形状: 立体矩形
      - 填充: #bbdefb (浅蓝色)
      - 边框: #1976d2 (深蓝色), 3px
      - 图标: Gateway Deployment图标

    Deployment Unit (业务):
      - 形状: 立体矩形
      - 填充: #e3f2fd (极浅蓝色)
      - 边框: #0277bd (中蓝色), 3px
      - 图标: Business Deployment图标

    Deployment Unit (供应商):
      - 形状: 立体矩形
      - 填充: #f3e5f5 (浅紫色)
      - 边框: #7b1fa2 (深紫色), 3px
      - 图标: Supplier Deployment图标

    Deployment Unit (数据):
      - 形状: 立体矩形
      - 填充: #e8f5e8 (浅绿色)
      - 边框: #388e3c (深绿色), 3px
      - 图标: Data Processing Deployment图标

    Deployment Unit (平台):
      - 形状: 立体矩形
      - 填充: #fff3e0 (浅橙色)
      - 边框: #ff8f00 (深橙色), 3px
      - 图标: Platform Deployment图标

    Infrastructure Deployment (数据库):
      - 形状: 立体矩形
      - 填充: #ffcc80 (浅橙色)
      - 边框: #f57c00 (深橙色), 3px
      - 图标: Database Infrastructure图标

    Infrastructure Deployment (中间件):
      - 形状: 立体矩形
      - 填充: #ffcdd2 (浅红色)
      - 边框: #d32f2f (深红色), 3px
      - 图标: Middleware Infrastructure图标

  关系设置:
    部署依赖: 实线箭头，表示部署时的依赖关系
    运行时依赖: 虚线箭头，表示运行时的数据流
    基础设施访问: 点线箭头，表示对基础设施的访问

  布局原则:
    - 按照部署层次从上到下排列
    - 同层部署单元水平排列
    - 基础设施部署在底部
    - 依赖关系尽量避免交叉
```

#### 📊 部署分组说明

| 部署组 | 部署单元 | 部署特点 | 运维重点 | 资源需求 |
|--------|----------|----------|----------|----------|
| **🟢 前端部署组** | Web前端集群 | 静态资源 + CDN | 版本发布管理 | 低CPU，高带宽 |
| | 移动端部署 | 应用商店发布 | 版本兼容性 | 客户端更新 |
| **🔵 网关部署组** | API网关集群 | 高可用负载均衡 | 流量监控 | 中等CPU，高网络 |
| | 认证服务集群 | 会话共享 | 安全性监控 | 中等CPU，高内存 |
| **🔵 核心业务部署组** | 管理服务集群 | 业务逻辑集中 | 业务监控 | 高CPU，高内存 |
| | ERP服务部署 | 独立资源池 | 数据一致性 | 高CPU，高内存，高IO |
| | 交易服务集群 | 事务处理 | 性能监控 | 高CPU，高内存 |
| **🔵 供应商服务部署组** | 传统供应商集群 | 业务隔离 | 供应商SLA | 中等资源 |
| | Galaxy服务集群 | 新业务扩展 | 扩展性监控 | 中等资源 |
| **🔵 数据服务部署组** | 数据处理集群 | 计算密集型 | 数据质量监控 | 高CPU，高内存 |
| | 消息服务集群 | 实时性要求 | 消息延迟监控 | 中等CPU，高网络 |
| **🔵 平台服务部署组** | 监控管理集群 | 系统监控 | 监控系统自身 | 中等资源 |
| | 调度服务部署 | 任务调度 | 任务执行监控 | 低资源 |
| **🟠 数据库部署组** | 缓存集群 | 高可用分片 | 缓存命中率 | 高内存，高网络 |
| | 关系数据库集群 | 主从复制 | 数据备份恢复 | 高IO，高内存 |
| | NoSQL集群 | 副本集群 | 数据一致性 | 高IO，高存储 |
| **🟠 中间件部署组** | 消息中间件 | 集群高可用 | 消息堆积监控 | 中等资源，高IO |
| | 配置中心 | 配置管理 | 配置变更监控 | 低资源 |

### 🏗️ 架构视角5: 治理分组视图 (Governance Grouping View)

> **设计理念**: 按照架构治理和管控角度进行分组，突出架构管理便利性
> **理论依据**: Architecture Governance Framework + TOGAF Architecture Governance
> **ArchiMate视图类型**: Motivation View + Strategy View with Grouping Elements

#### 📋 治理分组拓扑

```mermaid
graph TD
    subgraph "🟢 用户体验治理组 (User Experience Governance)"
        UEG_FRONTEND["🎨 前端体验治理<br/><i>Governance Group</i><br/>• 5个前端应用<br/>• UI/UX一致性<br/>• 用户体验标准"]

        UEG_AUTH["🔐 认证体验治理<br/><i>Governance Group</i><br/>• sso_project<br/>• maxkey<br/>• 统一认证体验"]
    end

    subgraph "🔵 业务治理组 (Business Governance)"
        BG_CORE["🏢 核心业务治理<br/><i>Governance Group</i><br/>• galaxy_odoo_erp<br/>• bid-system-admin<br/>• common<br/>• 业务规则统一"]

        BG_TRADING["💰 交易业务治理<br/><i>Governance Group</i><br/>• online-bidding<br/>• offerbids<br/>• bid_orders<br/>• 交易流程标准"]

        BG_SUPPLIER["🏭 供应商业务治理<br/><i>Governance Group</i><br/>• 6个供应商服务<br/>• 供应商接入标准<br/>• 业务流程一致性"]
    end

    subgraph "🔵 技术治理组 (Technical Governance)"
        TG_GATEWAY["🚪 网关技术治理<br/><i>Governance Group</i><br/>• sonic-gateway<br/>• API标准化<br/>• 接口版本管理"]

        TG_DATA["📊 数据技术治理<br/><i>Governance Group</i><br/>• express<br/>• syncdata<br/>• hyla<br/>• docomo<br/>• 数据标准化"]

        TG_MESSAGE["📨 消息技术治理<br/><i>Governance Group</i><br/>• push-access-service<br/>• push-send-service<br/>• 消息标准化"]
    end

    subgraph "🔵 运维治理组 (Operations Governance)"
        OG_MONITOR["📊 监控治理<br/><i>Governance Group</i><br/>• sonic-admin<br/>• 监控标准化<br/>• SLA管理"]

        OG_SCHEDULE["⏰ 调度治理<br/><i>Governance Group</i><br/>• xxl-job<br/>• 任务标准化<br/>• 调度策略统一"]
    end

    subgraph "🟠 基础设施治理组 (Infrastructure Governance)"
        IG_STORAGE["💾 存储治理<br/><i>Infrastructure Governance</i><br/>• Redis<br/>• MySQL<br/>• MongoDB<br/>• PostgreSQL<br/>• 存储标准化"]

        IG_COMMUNICATION["📮 通信治理<br/><i>Infrastructure Governance</i><br/>• RabbitMQ<br/>• Nacos<br/>• Elasticsearch<br/>• 通信协议标准"]
    end

    subgraph "🔍 架构治理中心 (Architecture Governance Center)"
        AGC_STANDARDS["📋 架构标准<br/><i>Architecture Principle</i><br/>• 设计原则<br/>• 技术标准<br/>• 最佳实践"]

        AGC_COMPLIANCE["✅ 合规检查<br/><i>Architecture Compliance</i><br/>• 架构审查<br/>• 标准检查<br/>• 质量门禁"]

        AGC_EVOLUTION["🚀 架构演进<br/><i>Architecture Evolution</i><br/>• 技术路线图<br/>• 架构升级<br/>• 创新推动"]
    end

    %% 🔵 治理关系
    AGC_STANDARDS --> UEG_FRONTEND
    AGC_STANDARDS --> UEG_AUTH
    AGC_STANDARDS --> BG_CORE
    AGC_STANDARDS --> BG_TRADING
    AGC_STANDARDS --> BG_SUPPLIER
    AGC_STANDARDS --> TG_GATEWAY
    AGC_STANDARDS --> TG_DATA
    AGC_STANDARDS --> TG_MESSAGE
    AGC_STANDARDS --> OG_MONITOR
    AGC_STANDARDS --> OG_SCHEDULE
    AGC_STANDARDS --> IG_STORAGE
    AGC_STANDARDS --> IG_COMMUNICATION

    AGC_COMPLIANCE --> UEG_FRONTEND
    AGC_COMPLIANCE --> UEG_AUTH
    AGC_COMPLIANCE --> BG_CORE
    AGC_COMPLIANCE --> BG_TRADING
    AGC_COMPLIANCE --> BG_SUPPLIER
    AGC_COMPLIANCE --> TG_GATEWAY
    AGC_COMPLIANCE --> TG_DATA
    AGC_COMPLIANCE --> TG_MESSAGE
    AGC_COMPLIANCE --> OG_MONITOR
    AGC_COMPLIANCE --> OG_SCHEDULE

    AGC_EVOLUTION --> BG_CORE
    AGC_EVOLUTION --> TG_GATEWAY
    AGC_EVOLUTION --> TG_DATA

    %% 🔵 业务治理关系
    UEG_FRONTEND --> UEG_AUTH
    UEG_AUTH --> TG_GATEWAY
    TG_GATEWAY --> BG_CORE
    BG_CORE --> BG_TRADING
    BG_CORE --> BG_SUPPLIER
    BG_TRADING --> TG_DATA
    BG_SUPPLIER --> TG_DATA
    TG_DATA --> TG_MESSAGE
    OG_MONITOR --> BG_CORE
    OG_MONITOR --> TG_GATEWAY
    OG_SCHEDULE --> BG_CORE
    OG_SCHEDULE --> TG_DATA

    %% 🟠 基础设施治理关系
    UEG_AUTH -.-> IG_STORAGE
    TG_GATEWAY -.-> IG_STORAGE
    TG_GATEWAY -.-> IG_COMMUNICATION
    BG_CORE -.-> IG_STORAGE
    BG_CORE -.-> IG_COMMUNICATION
    BG_TRADING -.-> IG_STORAGE
    BG_SUPPLIER -.-> IG_STORAGE
    BG_SUPPLIER -.-> IG_COMMUNICATION
    TG_DATA -.-> IG_STORAGE
    TG_DATA -.-> IG_COMMUNICATION
    TG_MESSAGE -.-> IG_STORAGE
    TG_MESSAGE -.-> IG_COMMUNICATION
    OG_SCHEDULE -.-> IG_STORAGE

    %% ArchiMate 3.1 标准颜色规范
    classDef userGovernance fill:#c8e6c9,stroke:#2e7d32,stroke-width:3px,color:#1b5e20
    classDef businessGovernance fill:#bbdefb,stroke:#1976d2,stroke-width:3px,color:#0d47a1
    classDef technicalGovernance fill:#e3f2fd,stroke:#0277bd,stroke-width:3px,color:#01579b
    classDef operationsGovernance fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c
    classDef infrastructureGovernance fill:#ffcc80,stroke:#f57c00,stroke-width:3px,color:#e65100
    classDef architectureGovernance fill:#ffcdd2,stroke:#d32f2f,stroke-width:3px,color:#b71c1c

    class UEG_FRONTEND,UEG_AUTH userGovernance
    class BG_CORE,BG_TRADING,BG_SUPPLIER businessGovernance
    class TG_GATEWAY,TG_DATA,TG_MESSAGE technicalGovernance
    class OG_MONITOR,OG_SCHEDULE operationsGovernance
    class IG_STORAGE,IG_COMMUNICATION infrastructureGovernance
    class AGC_STANDARDS,AGC_COMPLIANCE,AGC_EVOLUTION architectureGovernance
```

#### **ArchiMate图例说明 - 治理分组视图**

##### **Visual Paradigm ArchiMate元素映射**

| ArchiMate层次 | Visual Paradigm元素 | 颜色编码 | 图形符号 | 在本视图中的应用 |
|---------------|-------------------|---------|----------|-----------------|
| **🟢 业务层** | Governance Group | 绿色 `#c8e6c9` | 六边形 + 治理图标 | 用户体验治理组 - 业务层面的治理 |
| **🔵 应用层** | Governance Group | 蓝色 `#bbdefb` | 六边形 + 业务图标 | 业务治理组 - 应用业务的治理 |
| **🔵 应用层** | Governance Group | 浅蓝色 `#e3f2fd` | 六边形 + 技术图标 | 技术治理组 - 应用技术的治理 |
| **🔵 应用层** | Governance Group | 紫色 `#f3e5f5` | 六边形 + 运维图标 | 运维治理组 - 应用运维的治理 |
| **🟠 技术层** | Infrastructure Governance | 橙色 `#ffcc80` | 六边形 + 基础设施图标 | 基础设施治理组 - 技术基础设施的治理 |
| **🔍 治理层** | Architecture Principle | 红色 `#ffcdd2` | 菱形 + 原则图标 | 架构标准 - 架构设计原则 |
| **🔍 治理层** | Architecture Compliance | 红色 `#ffcdd2` | 菱形 + 检查图标 | 合规检查 - 架构合规性检查 |
| **🔍 治理层** | Architecture Evolution | 红色 `#ffcdd2` | 菱形 + 演进图标 | 架构演进 - 架构演进管理 |

##### **ArchiMate 3.1 关系类型**

| 关系样式 | ArchiMate关系 | Visual Paradigm表示 | 含义 | 在本视图中的使用 |
|---------|---------------|-------------------|------|-----------------|
| **🔵 蓝色实线箭头** | Serving Relationship | 实线箭头 `→` | 服务关系 | 治理组间的治理服务关系 |
| **🔵 蓝色实线箭头** | Influence Relationship | 实线箭头 `⟶` | 影响关系 | 架构治理中心对各治理组的影响 |
| **🟠 橙色点线箭头** | Access Relationship | 点线箭头 `⇣` | 访问关系 | 治理组对基础设施治理的访问 |

##### **Visual Paradigm绘制指南**

```yaml
Visual Paradigm设置:
  图表类型: ArchiMate Motivation View + Implementation View

  治理层次布局:
    - 架构治理中心 (中央) - 核心治理机制
    - 用户体验治理组 (左上) - 业务层治理
    - 业务治理组 (上中) - 应用业务治理
    - 技术治理组 (右上) - 应用技术治理
    - 运维治理组 (左下) - 应用运维治理
    - 基础设施治理组 (下中) - 技术基础设施治理

  元素设置:
    Governance Group (用户体验):
      - 形状: 六边形
      - 填充: #c8e6c9 (浅绿色)
      - 边框: #2e7d32 (深绿色), 3px
      - 图标: User Experience Governance图标

    Governance Group (业务):
      - 形状: 六边形
      - 填充: #bbdefb (浅蓝色)
      - 边框: #1976d2 (深蓝色), 3px
      - 图标: Business Governance图标

    Governance Group (技术):
      - 形状: 六边形
      - 填充: #e3f2fd (极浅蓝色)
      - 边框: #0277bd (中蓝色), 3px
      - 图标: Technical Governance图标

    Governance Group (运维):
      - 形状: 六边形
      - 填充: #f3e5f5 (浅紫色)
      - 边框: #7b1fa2 (深紫色), 3px
      - 图标: Operations Governance图标

    Infrastructure Governance:
      - 形状: 六边形
      - 填充: #ffcc80 (浅橙色)
      - 边框: #f57c00 (深橙色), 3px
      - 图标: Infrastructure Governance图标

    Architecture Principle:
      - 形状: 菱形
      - 填充: #ffcdd2 (浅红色)
      - 边框: #d32f2f (深红色), 3px
      - 图标: Principle图标

    Architecture Compliance:
      - 形状: 菱形
      - 填充: #ffcdd2 (浅红色)
      - 边框: #d32f2f (深红色), 3px
      - 图标: Assessment图标

    Architecture Evolution:
      - 形状: 菱形
      - 填充: #ffcdd2 (浅红色)
      - 边框: #d32f2f (深红色), 3px
      - 图标: Goal图标

  关系设置:
    治理影响: 从架构治理中心向各治理组的影响关系
    治理协作: 治理组之间的协作关系
    治理访问: 治理组对基础设施治理的访问关系

  布局原则:
    - 架构治理中心位于图表中央
    - 各治理组围绕中心分布
    - 治理关系呈放射状
    - 避免关系线交叉
```

#### 📊 治理分组说明

| 治理组 | 治理范围 | 治理重点 | 治理目标 | 治理价值 |
|--------|----------|----------|----------|----------|
| **🟢 用户体验治理组** | 前端体验治理 | UI/UX一致性 | 用户体验统一 | 用户满意度提升 |
| | 认证体验治理 | 认证流程标准化 | 安全体验平衡 | 安全性与易用性 |
| **🔵 业务治理组** | 核心业务治理 | 业务规则统一 | 业务流程标准化 | 业务效率提升 |
| | 交易业务治理 | 交易流程标准 | 交易安全可靠 | 业务风险控制 |
| | 供应商业务治理 | 供应商接入标准 | 供应商管理统一 | 供应商关系优化 |
| **🔵 技术治理组** | 网关技术治理 | API标准化 | 接口统一管理 | 技术债务减少 |
| | 数据技术治理 | 数据标准化 | 数据质量保障 | 数据价值提升 |
| | 消息技术治理 | 消息标准化 | 通信协议统一 | 系统集成简化 |
| **🔵 运维治理组** | 监控治理 | 监控标准化 | SLA管理统一 | 运维效率提升 |
| | 调度治理 | 任务标准化 | 调度策略统一 | 系统自动化 |
| **🟠 基础设施治理组** | 存储治理 | 存储标准化 | 数据管理统一 | 存储成本优化 |
| | 通信治理 | 通信协议标准 | 通信管理统一 | 通信效率提升 |
| **🔍 架构治理中心** | 架构标准 | 设计原则制定 | 架构标准化 | 架构质量保障 |
| | 合规检查 | 标准合规性 | 质量门禁 | 架构风险控制 |
| | 架构演进 | 技术路线图 | 架构升级 | 技术创新推动 |

## 🎯 分组架构视角总结

### 📊 五个架构视角对比

| 架构视角 | 分组原则 | 主要价值 | 适用场景 | 目标受众 |
|---------|----------|----------|----------|----------|
| **🏗️ 业务功能分组** | 按业务功能域分组 | 业务价值链清晰 | 业务分析、需求梳理 | 业务分析师、产品经理 |
| **🏗️ 技术分层分组** | 按技术架构分层 | 技术实现层次清晰 | 技术设计、开发指导 | 架构师、开发团队 |
| **🏗️ 数据流分组** | 按数据流向分组 | 数据处理链路清晰 | 数据治理、性能优化 | 数据架构师、DBA |
| **🏗️ 部署分组** | 按部署运维分组 | 运维管理便利性 | 部署规划、运维管理 | 运维团队、DevOps |
| **🏗️ 治理分组** | 按架构治理分组 | 架构管控便利性 | 架构治理、标准化 | 架构委员会、CTO |

### 🎨 架构视角应用建议

#### 1. **业务讨论时** - 使用业务功能分组视图
- **优势**: 业务人员容易理解，突出业务价值
- **场景**: 业务需求分析、投资决策、业务流程梳理
- **重点**: 用户接入域 → 核心业务域 → 供应商业务域的价值链

#### 2. **技术设计时** - 使用技术分层分组视图
- **优势**: 技术实现清晰，分层职责明确
- **场景**: 系统设计、技术选型、开发指导
- **重点**: 表现层 → 服务层 → 集成层 → 数据层的技术栈

#### 3. **数据治理时** - 使用数据流分组视图
- **优势**: 数据流向清晰，数据处理链路明确
- **场景**: 数据治理、性能优化、数据质量管理
- **重点**: 数据源 → 接入 → 处理 → 同步 → 存储的数据链路

#### 4. **运维管理时** - 使用部署分组视图
- **优势**: 部署单元清晰，运维职责明确
- **场景**: 部署规划、容量规划、故障处理
- **重点**: 前端 → 网关 → 业务 → 数据 → 基础设施的部署层次

#### 5. **架构治理时** - 使用治理分组视图
- **优势**: 治理范围清晰，标准化管理便利
- **场景**: 架构审查、标准制定、合规检查
- **重点**: 用户体验 → 业务 → 技术 → 运维 → 基础设施的治理维度

### 🚀 架构简化建议

基于以上分组分析，我们识别出以下架构简化机会：

#### 1. **服务合并机会**
- **前端应用**: 5个前端应用可考虑合并为统一的前端平台
- **供应商服务**: 6个供应商服务可抽象为统一的供应商适配器模式
- **推送服务**: 2个推送服务可合并为统一的消息推送平台

#### 2. **技术栈统一机会**
- **前端技术栈**: 统一为Vue 3 + TypeScript
- **后端技术栈**: 统一为Spring Boot + Java 17
- **数据处理**: 考虑将C++引擎集成到Java服务中

#### 3. **基础设施优化机会**
- **缓存统一**: 多个Redis实例可合并为统一的Redis集群
- **数据库整合**: 考虑将MongoDB数据迁移到PostgreSQL
- **消息队列**: 统一使用RabbitMQ，减少Redis pub/sub使用

## 🚀 架构演进方向

基于当前架构分析和多视角分组整合，我们识别出以下关键的技术演进需求：

### 主要改进方向
1. **简化服务架构**: 减少微服务数量，降低维护复杂度
2. **统一技术栈**: 减少技术栈多样性，提升开发效率
3. **消除技术债务**: 解决架构反模式和设计问题
4. **提升系统稳定性**: 减少级联故障风险

### 基于分组视角的演进策略
1. **业务功能整合**: 基于业务功能分组，合并相似业务服务
2. **技术栈标准化**: 基于技术分层分组，统一各层技术选型
3. **数据流优化**: 基于数据流分组，简化数据处理链路
4. **部署标准化**: 基于部署分组，优化运维管理流程
5. **治理体系建设**: 基于治理分组，建立完善的架构治理机制

### 具体解决方案
**详细的目标架构设计和实施方案请参考**:
- **[目标架构技术设计](TARGET_ARCHITECTURE.md)** - 基于Odoo的统一平台详细设计
- **[实施计划](../project/IMPLEMENTATION_PLAN.md)** - 6个月转型实施计划
- **[目标架构摘要](../../TARGET_ARCHITECTURE_SUMMARY.md)** - 高管层投资决策报告

## 📋 架构决策记录

### ADR-001: 微服务架构选择
- **决策**: 采用微服务架构按供应商维度拆分
- **理由**: 业务隔离性好，支持独立部署和扩展
- **影响**: 增加了系统复杂度，但提高了业务灵活性

### ADR-002: 多技术栈策略
- **决策**: Java主栈 + Python数据处理 + C++高性能引擎
- **理由**: 发挥各技术栈优势，满足不同场景需求
- **影响**: 提高了维护成本，但获得了性能优势

### ADR-003: Redis作为主要缓存
- **决策**: 使用Redis作为缓存和消息队列
- **理由**: 高性能，支持多种数据结构
- **影响**: 对Redis依赖较重，需要考虑高可用

## 🔍 关键指标

### 性能指标
- **响应时间**: API平均响应时间 < 200ms
- **吞吐量**: 支持1000+ TPS并发处理
- **可用性**: 目标99.9%系统可用性

### 质量指标
- **代码覆盖率**: 目标80%+单元测试覆盖
- **故障恢复**: 目标5分钟内故障恢复
- **部署频率**: 支持每日多次部署

---

**文档版本**: v7.0
**适用对象**: CTO、架构师、技术负责人、开发团队、运维团队、业务分析师
**文档定位**: 现有架构分析和多视角分组整合展示
**主要更新**:
- ✅ **新增5个功能分组架构视角**：基于功能、技术、数据流、部署、治理的分组整合
- ✅ **业务功能分组视图**：按业务功能域分组，突出业务价值链
- ✅ **技术分层分组视图**：按技术架构分层，突出技术实现层次
- ✅ **数据流分组视图**：按数据流向分组，突出数据处理链路
- ✅ **部署分组视图**：按部署运维分组，突出运维管理便利性
- ✅ **治理分组视图**：按架构治理分组，突出架构管控便利性
- ✅ **架构简化建议**：基于分组分析提出服务合并和技术栈统一建议
- ✅ **多视角对比分析**：提供5个架构视角的对比和应用建议
- ✅ **遵循ArchiMate 3.1标准**：所有新增视图都严格遵循ArchiMate规范
- ✅ **Visual Paradigm兼容**：所有图表都可在Visual Paradigm中重现

**架构视角结构**:
- 🏗️ **视角1: 业务功能分组** - 6个功能域，突出业务价值链
- 🏗️ **视角2: 技术分层分组** - 5个技术层，突出技术实现层次
- 🏗️ **视角3: 数据流分组** - 7个数据层，突出数据处理链路
- 🏗️ **视角4: 部署分组** - 8个部署组，突出运维管理便利性
- 🏗️ **视角5: 治理分组** - 5个治理组 + 架构治理中心

**传统子图结构** (保持不变):
- 📱 子图1: 前端认证层 (8个服务, 15个依赖)
- 🏢 子图2: 核心管理层 (4个服务, 12个依赖)
- 📡 子图3: 传统供应商业务 (6个服务, 15个依赖)
- 🎯 子图4: Galaxy业务生态 (7个服务, 14个依赖)
- 📋 子图5: 订单管理关系 (8个服务, 10个依赖)
- 📊 子图6: 管理服务关系 (12个服务, 14个依赖)
- 📈 子图7: 数据服务关系 (3个服务, 3个依赖)
- 🏢 子图8: ERP核心业务层 (8个服务, 12个依赖)
- ⚡ 子图9: 数据处理层 (13个服务, 25个依赖)
- 🔧 子图10: 基础设施层 (6个组件, 26个依赖)

**更新频率**: 每月或重大架构变更时
**相关文档**: [服务依赖分析](SERVICE_DEPENDENCIES.md) | [ERP依赖分析](ERP_DEPENDENCY_ANALYSIS.md) | [风险评估](RISK_ASSESSMENT.md) | [目标架构设计](TARGET_ARCHITECTURE.md)
